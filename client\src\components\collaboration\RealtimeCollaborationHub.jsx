import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Tabs, <PERSON>b, <PERSON><PERSON>r, Badge, Select, SelectItem } from '@heroui/react';
import { Users, MessageCircle, Activity, Settings, Maximize2, Minimize2, Wifi, WifiOff, ChevronDown } from 'lucide-react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { realtimeCollaboration } from '../../services/realtimeCollaboration.service';
import { supabase } from '../../utils/supabase/supabase.utils';
import PresenceIndicator from './PresenceIndicator';
import RealtimeProjectChat from './RealtimeProjectChat';
import RealtimeTaskUpdates from './RealtimeTaskUpdates';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Real-Time Collaboration Hub Component
 *
 * Central hub for all real-time collaboration features including
 * team presence, instant messaging, live updates, and activity feeds.
 * Supports both team and project-based collaboration for gigwork platform.
 */
const RealtimeCollaborationHub = ({ projectId, isVisible = true, onToggleVisibility }) => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('presence');
  const [isExpanded, setIsExpanded] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState({});
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [recentUpdates, setRecentUpdates] = useState(0);

  // Collaboration context state
  const [collaborationMode, setCollaborationMode] = useState('team'); // 'team' or 'project'
  const [userTeams, setUserTeams] = useState([]);
  const [userProjects, setUserProjects] = useState([]);
  const [selectedTeamId, setSelectedTeamId] = useState(null);
  const [selectedProjectId, setSelectedProjectId] = useState(projectId);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null);
  const [teamsLoading, setTeamsLoading] = useState(false);
  const [projectsLoading, setProjectsLoading] = useState(false);

  // Fetch user's teams/studios
  const fetchUserTeams = async () => {
    if (!currentUser) return;

    try {
      setTeamsLoading(true);

      // Get team memberships
      const { data: memberships, error: membershipError } = await supabase
        .from('team_members')
        .select('*')
        .eq('user_id', currentUser.id)
        .eq('status', 'active');

      if (membershipError) throw membershipError;

      if (!memberships || memberships.length === 0) {
        setUserTeams([]);
        return;
      }

      // Get team details
      const teamIds = memberships.map(m => m.team_id);
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select(`
          id,
          name,
          description,
          studio_type,
          created_at
        `)
        .in('id', teamIds);

      if (teamsError) throw teamsError;

      // Combine team data with membership info
      const teamsWithRoles = teamsData.map(team => {
        const membership = memberships.find(m => m.team_id === team.id);
        return {
          ...team,
          role: membership?.role || 'member',
          is_admin: membership?.is_admin || false,
          collaboration_type: membership?.collaboration_type
        };
      });

      setUserTeams(teamsWithRoles);

      // Auto-select first team if none selected and in team mode
      if (collaborationMode === 'team' && !selectedTeamId && teamsWithRoles.length > 0) {
        setSelectedTeamId(teamsWithRoles[0].id);
        setSelectedTeam(teamsWithRoles[0]);
      }

    } catch (error) {
      console.error('Error fetching user teams:', error);
      toast.error('Failed to load teams');
    } finally {
      setTeamsLoading(false);
    }
  };

  // Fetch user's projects
  const fetchUserProjects = async () => {
    if (!currentUser) return;

    try {
      setProjectsLoading(true);

      // First get projects created by user
      const { data: ownedProjects, error: ownedError } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          title,
          description,
          status,
          created_by,
          team_id,
          created_at
        `)
        .eq('created_by', currentUser.id)
        .eq('is_active', true);

      if (ownedError) throw ownedError;

      // Then get projects from teams user is member of
      const { data: teamMemberships, error: memberError } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', currentUser.id)
        .eq('status', 'active');

      if (memberError) throw memberError;

      let teamProjects = [];
      if (teamMemberships && teamMemberships.length > 0) {
        const teamIds = teamMemberships.map(m => m.team_id);
        const { data: teamProjectsData, error: teamProjectsError } = await supabase
          .from('projects')
          .select(`
            id,
            name,
            title,
            description,
            status,
            created_by,
            team_id,
            created_at
          `)
          .in('team_id', teamIds)
          .eq('is_active', true);

        if (teamProjectsError) throw teamProjectsError;
        teamProjects = teamProjectsData || [];
      }

      // Combine and deduplicate projects
      const allProjects = [...(ownedProjects || []), ...teamProjects];
      const uniqueProjects = allProjects.filter((project, index, self) =>
        index === self.findIndex(p => p.id === project.id)
      );

      // Get team names for projects
      const projectsWithTeamNames = await Promise.all(
        uniqueProjects.map(async (project) => {
          let teamName = 'Personal Project';
          if (project.team_id) {
            const { data: team } = await supabase
              .from('teams')
              .select('name')
              .eq('id', project.team_id)
              .single();
            teamName = team?.name || 'Unknown Team';
          }

          return {
            ...project,
            isOwner: project.created_by === currentUser.id,
            teamName
          };
        })
      );

      // Sort by creation date
      projectsWithTeamNames.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      setUserProjects(projectsWithTeamNames);

      // Auto-select current project or first project if none selected and in project mode
      if (collaborationMode === 'project') {
        if (projectId && projectsWithTeamNames.find(p => p.id === projectId)) {
          setSelectedProjectId(projectId);
          setSelectedProject(projectsWithTeamNames.find(p => p.id === projectId));
        } else if (!selectedProjectId && projectsWithTeamNames.length > 0) {
          setSelectedProjectId(projectsWithTeamNames[0].id);
          setSelectedProject(projectsWithTeamNames[0]);
        }
      }

    } catch (error) {
      console.error('Error fetching user projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setProjectsLoading(false);
    }
  };

  // Handle collaboration mode change
  const handleModeChange = (mode) => {
    setCollaborationMode(mode);

    // Reset unread counts when switching modes
    setUnreadMessages(0);
    setRecentUpdates(0);

    // Auto-select appropriate item based on mode
    if (mode === 'team' && userTeams.length > 0 && !selectedTeamId) {
      setSelectedTeamId(userTeams[0].id);
      setSelectedTeam(userTeams[0]);
    } else if (mode === 'project' && userProjects.length > 0 && !selectedProjectId) {
      const defaultProject = projectId ? userProjects.find(p => p.id === projectId) : userProjects[0];
      if (defaultProject) {
        setSelectedProjectId(defaultProject.id);
        setSelectedProject(defaultProject);
      }
    }

    toast.success(`Switched to ${mode} collaboration`);
  };

  // Handle team selection change
  const handleTeamChange = (teamId) => {
    const team = userTeams.find(t => t.id === teamId);
    setSelectedTeamId(teamId);
    setSelectedTeam(team);

    // Reset unread counts when switching teams
    setUnreadMessages(0);
    setRecentUpdates(0);

    toast.success(`Switched to ${team?.name || 'team'}`);
  };

  // Handle project selection change
  const handleProjectChange = (projectId) => {
    const project = userProjects.find(p => p.id === projectId);
    setSelectedProjectId(projectId);
    setSelectedProject(project);

    // Reset unread counts when switching projects
    setUnreadMessages(0);
    setRecentUpdates(0);

    toast.success(`Switched to ${project?.name || project?.title || 'project'}`);
  };

  // Initialize teams and projects on component mount
  useEffect(() => {
    if (currentUser) {
      fetchUserTeams();
      fetchUserProjects();
    }
  }, [currentUser]);

  // Update selected project when projectId prop changes
  useEffect(() => {
    if (projectId && userProjects.length > 0) {
      const project = userProjects.find(p => p.id === projectId);
      if (project) {
        setSelectedProjectId(projectId);
        setSelectedProject(project);
        // Switch to project mode if we have a specific project
        if (collaborationMode !== 'project') {
          setCollaborationMode('project');
        }
      }
    }
  }, [projectId, userProjects]);

  useEffect(() => {
    if (!currentUser) return;

    let isInitialized = false;

    const initializeHub = async () => {
      try {
        // Prevent multiple initializations
        if (isInitialized) return;
        isInitialized = true;

        // Initialize collaboration service
        await realtimeCollaboration.initialize(currentUser);

        // Set up event listeners for connection status
        realtimeCollaboration.addEventListener('project_connected', handleProjectConnected);
        realtimeCollaboration.addEventListener('message_update', handleMessageUpdate);
        realtimeCollaboration.addEventListener('task_update', handleTaskUpdate);

        setIsConnected(true);

        // Get initial connection status
        const status = realtimeCollaboration.getConnectionStatus();
        setConnectionStatus(status);

        console.log('🚀 Collaboration Hub initialized');
      } catch (error) {
        console.error('Error initializing collaboration hub:', error);
        setIsConnected(false);
        toast.error('Failed to connect to collaboration features');
        isInitialized = false;
      }
    };

    initializeHub();

    return () => {
      // Clean up event listeners
      if (isInitialized) {
        realtimeCollaboration.removeEventListener('project_connected', handleProjectConnected);
        realtimeCollaboration.removeEventListener('message_update', handleMessageUpdate);
        realtimeCollaboration.removeEventListener('task_update', handleTaskUpdate);
      }
    };
  }, [currentUser]);

  // Handle project connection
  const handleProjectConnected = (data) => {
    console.log('📡 Project connected:', data);
    setIsConnected(true);
    toast.success('Connected to real-time collaboration', {
      icon: '🚀',
      duration: 2000
    });
  };

  // Handle message updates
  const handleMessageUpdate = (data) => {
    if (data.eventType === 'INSERT' && data.data.sender_id !== currentUser?.id) {
      setUnreadMessages(prev => prev + 1);
      
      // Show notification if not on chat tab
      if (activeTab !== 'chat') {
        toast.success(`New message from ${data.data.metadata?.sender_name || 'Team member'}`, {
          icon: '💬',
          duration: 3000
        });
      }
    }
  };

  // Handle task updates
  const handleTaskUpdate = (data) => {
    setRecentUpdates(prev => prev + 1);
    
    // Show notification if not on updates tab
    if (activeTab !== 'updates') {
      toast.success('Task updated', {
        icon: '📋',
        duration: 2000
      });
    }
  };

  // Toggle expanded view
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Handle tab change
  const handleTabChange = (key) => {
    setActiveTab(key);
    
    // Clear unread counts when switching to respective tabs
    if (key === 'chat') {
      setUnreadMessages(0);
    } else if (key === 'updates') {
      setRecentUpdates(0);
    }
  };

  // Render connection status
  const renderConnectionStatus = () => {
    return (
      <div className="flex items-center gap-2">
        {isConnected ? (
          <>
            <Wifi size={16} className="text-success" />
            <span className="text-xs text-success">Connected</span>
          </>
        ) : (
          <>
            <WifiOff size={16} className="text-danger" />
            <span className="text-xs text-danger">Disconnected</span>
          </>
        )}
      </div>
    );
  };

  // Render compact view
  const renderCompactView = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <Card className="w-80 shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Users size={18} className="text-primary" />
                <h4 className="font-semibold">
                  {collaborationMode === 'team' ? 'Team' : 'Project'} Collaboration
                </h4>
              </div>
              
              <div className="flex items-center gap-2">
                {renderConnectionStatus()}
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={toggleExpanded}
                >
                  <Maximize2 size={16} />
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardBody className="pt-0">
            {/* Mode Selection Tabs */}
            {(userTeams.length > 0 || userProjects.length > 0) && (
              <div className="mb-3">
                <div className="flex gap-1 p-1 bg-default-100 rounded-lg">
                  <Button
                    size="sm"
                    variant={collaborationMode === 'team' ? 'solid' : 'light'}
                    color={collaborationMode === 'team' ? 'primary' : 'default'}
                    onPress={() => handleModeChange('team')}
                    className="flex-1"
                    isDisabled={userTeams.length === 0}
                  >
                    Teams
                  </Button>
                  <Button
                    size="sm"
                    variant={collaborationMode === 'project' ? 'solid' : 'light'}
                    color={collaborationMode === 'project' ? 'primary' : 'default'}
                    onPress={() => handleModeChange('project')}
                    className="flex-1"
                    isDisabled={userProjects.length === 0}
                  >
                    Projects
                  </Button>
                </div>
              </div>
            )}

            {/* Team Selection */}
            {collaborationMode === 'team' && userTeams.length > 0 && (
              <>
                {userTeams.length > 1 && (
                  <div className="mb-3">
                    <Select
                      size="sm"
                      placeholder="Select a team..."
                      selectedKeys={selectedTeamId ? [selectedTeamId] : []}
                      onSelectionChange={(keys) => {
                        const teamId = Array.from(keys)[0];
                        if (teamId) handleTeamChange(teamId);
                      }}
                      className="w-full"
                      isLoading={teamsLoading}
                      startContent={<Users size={14} />}
                    >
                      {userTeams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          <div className="flex items-center gap-2">
                            <span>{team.name}</span>
                            {team.role === 'owner' && <span className="text-xs text-primary">(Owner)</span>}
                            {team.is_admin && team.role !== 'owner' && <span className="text-xs text-secondary">(Admin)</span>}
                          </div>
                        </SelectItem>
                      ))}
                    </Select>
                  </div>
                )}

                {selectedTeam && (
                  <div className="mb-3 p-2 bg-default-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Users size={14} className="text-primary" />
                      <span className="text-sm font-medium">{selectedTeam.name}</span>
                      {selectedTeam.role === 'owner' && <span className="text-xs text-primary">(Owner)</span>}
                      {selectedTeam.is_admin && selectedTeam.role !== 'owner' && <span className="text-xs text-secondary">(Admin)</span>}
                    </div>
                    {selectedTeam.description && (
                      <p className="text-xs text-default-500 mt-1">{selectedTeam.description}</p>
                    )}
                  </div>
                )}
              </>
            )}

            {/* Project Selection */}
            {collaborationMode === 'project' && userProjects.length > 0 && (
              <>
                {userProjects.length > 1 && (
                  <div className="mb-3">
                    <Select
                      size="sm"
                      placeholder="Select a project..."
                      selectedKeys={selectedProjectId ? [selectedProjectId] : []}
                      onSelectionChange={(keys) => {
                        const projectId = Array.from(keys)[0];
                        if (projectId) handleProjectChange(projectId);
                      }}
                      className="w-full"
                      isLoading={projectsLoading}
                      startContent={<Activity size={14} />}
                    >
                      {userProjects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex items-center gap-2">
                            <span>{project.name || project.title}</span>
                            {project.isOwner && <span className="text-xs text-primary">(Owner)</span>}
                            <span className="text-xs text-default-500">({project.teamName})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </Select>
                  </div>
                )}

                {selectedProject && (
                  <div className="mb-3 p-2 bg-default-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Activity size={14} className="text-primary" />
                      <span className="text-sm font-medium">{selectedProject.name || selectedProject.title}</span>
                      {selectedProject.isOwner && <span className="text-xs text-primary">(Owner)</span>}
                    </div>
                    <p className="text-xs text-default-500 mt-1">{selectedProject.teamName}</p>
                    {selectedProject.description && (
                      <p className="text-xs text-default-500 mt-1">{selectedProject.description}</p>
                    )}
                  </div>
                )}
              </>
            )}

            <PresenceIndicator
              projectId={collaborationMode === 'team' ? selectedTeamId : selectedProjectId}
              showDetails={false}
            />
            
            <div className="flex gap-2 mt-3">
              <Button
                size="sm"
                variant="flat"
                color="primary"
                startContent={<MessageCircle size={14} />}
                onPress={() => {
                  setActiveTab('chat');
                  setIsExpanded(true);
                }}
                className="flex-1"
              >
                Chat
                {unreadMessages > 0 && (
                  <Badge content={unreadMessages} color="danger" size="sm" />
                )}
              </Button>
              
              <Button
                size="sm"
                variant="flat"
                color="secondary"
                startContent={<Activity size={14} />}
                onPress={() => {
                  setActiveTab('updates');
                  setIsExpanded(true);
                }}
                className="flex-1"
              >
                Updates
                {recentUpdates > 0 && (
                  <Badge content={recentUpdates} color="warning" size="sm" />
                )}
              </Button>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    );
  };

  // Render expanded view
  const renderExpandedView = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="fixed inset-4 z-50 bg-background/95 backdrop-blur-sm rounded-lg border border-divider shadow-2xl"
      >
        <Card className="h-full">
          <CardHeader className="flex-shrink-0">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <Users size={24} className="text-primary" />
                <div>
                  <h2 className="text-xl font-bold">Real-Time Collaboration Hub</h2>
                  <p className="text-sm text-default-500">
                    {collaborationMode === 'team' && selectedTeam
                      ? `${selectedTeam.name} team workspace`
                      : collaborationMode === 'project' && selectedProject
                      ? `${selectedProject.name || selectedProject.title} project workspace`
                      : `${collaborationMode} workspace`
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Mode Selection for Expanded View */}
                {(userTeams.length > 0 || userProjects.length > 0) && (
                  <div className="flex gap-1 p-1 bg-default-100 rounded-lg">
                    <Button
                      size="sm"
                      variant={collaborationMode === 'team' ? 'solid' : 'light'}
                      color={collaborationMode === 'team' ? 'primary' : 'default'}
                      onPress={() => handleModeChange('team')}
                      isDisabled={userTeams.length === 0}
                    >
                      Teams
                    </Button>
                    <Button
                      size="sm"
                      variant={collaborationMode === 'project' ? 'solid' : 'light'}
                      color={collaborationMode === 'project' ? 'primary' : 'default'}
                      onPress={() => handleModeChange('project')}
                      isDisabled={userProjects.length === 0}
                    >
                      Projects
                    </Button>
                  </div>
                )}

                {/* Team Selection Dropdown for Expanded View */}
                {collaborationMode === 'team' && userTeams.length > 1 && (
                  <Select
                    size="sm"
                    placeholder="Select a team..."
                    selectedKeys={selectedTeamId ? [selectedTeamId] : []}
                    onSelectionChange={(keys) => {
                      const teamId = Array.from(keys)[0];
                      if (teamId) handleTeamChange(teamId);
                    }}
                    className="w-64"
                    isLoading={teamsLoading}
                    startContent={<Users size={14} />}
                  >
                    {userTeams.map((team) => (
                      <SelectItem key={team.id} value={team.id}>
                        <div className="flex items-center gap-2">
                          <span>{team.name}</span>
                          {team.role === 'owner' && <span className="text-xs text-primary">(Owner)</span>}
                          {team.is_admin && team.role !== 'owner' && <span className="text-xs text-secondary">(Admin)</span>}
                        </div>
                      </SelectItem>
                    ))}
                  </Select>
                )}

                {/* Project Selection Dropdown for Expanded View */}
                {collaborationMode === 'project' && userProjects.length > 1 && (
                  <Select
                    size="sm"
                    placeholder="Select a project..."
                    selectedKeys={selectedProjectId ? [selectedProjectId] : []}
                    onSelectionChange={(keys) => {
                      const projectId = Array.from(keys)[0];
                      if (projectId) handleProjectChange(projectId);
                    }}
                    className="w-64"
                    isLoading={projectsLoading}
                    startContent={<Activity size={14} />}
                  >
                    {userProjects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        <div className="flex items-center gap-2">
                          <span>{project.name || project.title}</span>
                          {project.isOwner && <span className="text-xs text-primary">(Owner)</span>}
                          <span className="text-xs text-default-500">({project.teamName})</span>
                        </div>
                      </SelectItem>
                    ))}
                  </Select>
                )}

                {renderConnectionStatus()}
                <Button
                  isIconOnly
                  variant="light"
                  onPress={toggleExpanded}
                >
                  <Minimize2 size={20} />
                </Button>
              </div>
            </div>
          </CardHeader>

          <Divider />

          <CardBody className="flex-1 p-0">
            <Tabs
              selectedKey={activeTab}
              onSelectionChange={handleTabChange}
              className="h-full"
              classNames={{
                base: "h-full",
                tabList: "w-full",
                panel: "h-full p-0"
              }}
            >
              <Tab
                key="presence"
                title={
                  <div className="flex items-center gap-2">
                    <Users size={16} />
                    {collaborationMode === 'team' ? 'Team' : 'Project'} Presence
                  </div>
                }
              >
                <div className="h-full p-6">
                  <PresenceIndicator
                    projectId={collaborationMode === 'team' ? selectedTeamId : selectedProjectId}
                    showDetails={true}
                  />
                </div>
              </Tab>

              <Tab
                key="chat"
                title={
                  <div className="flex items-center gap-2">
                    <MessageCircle size={16} />
                    {collaborationMode === 'team' ? 'Team' : 'Project'} Chat
                    {unreadMessages > 0 && (
                      <Badge content={unreadMessages} color="danger" size="sm" />
                    )}
                  </div>
                }
              >
                <div className="h-full">
                  <RealtimeProjectChat
                    projectId={collaborationMode === 'team' ? selectedTeamId : selectedProjectId}
                    isVisible={true}
                  />
                </div>
              </Tab>

              <Tab
                key="updates"
                title={
                  <div className="flex items-center gap-2">
                    <Activity size={16} />
                    Live Updates
                    {recentUpdates > 0 && (
                      <Badge content={recentUpdates} color="warning" size="sm" />
                    )}
                  </div>
                }
              >
                <div className="h-full">
                  <RealtimeTaskUpdates
                    projectId={collaborationMode === 'team' ? selectedTeamId : selectedProjectId}
                    maxUpdates={50}
                  />
                </div>
              </Tab>

              <Tab
                key="settings"
                title={
                  <div className="flex items-center gap-2">
                    <Settings size={16} />
                    Settings
                  </div>
                }
              >
                <div className="h-full p-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Collaboration Settings</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 rounded-lg bg-default-50">
                          <div>
                            <p className="font-medium">Real-time Notifications</p>
                            <p className="text-sm text-default-500">Get notified of team activity</p>
                          </div>
                          <Button size="sm" color="primary" variant="flat">
                            Enabled
                          </Button>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 rounded-lg bg-default-50">
                          <div>
                            <p className="font-medium">Presence Tracking</p>
                            <p className="text-sm text-default-500">Show your online status to team</p>
                          </div>
                          <Button size="sm" color="primary" variant="flat">
                            Active
                          </Button>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 rounded-lg bg-default-50">
                          <div>
                            <p className="font-medium">Auto-sync Tasks</p>
                            <p className="text-sm text-default-500">Automatically sync task changes</p>
                          </div>
                          <Button size="sm" color="primary" variant="flat">
                            On
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Connection Status</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Active Subscriptions:</span>
                          <span className="font-medium">{connectionStatus.activeSubscriptions || 0}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Presence Channels:</span>
                          <span className="font-medium">{connectionStatus.activePresenceChannels || 0}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Connection Status:</span>
                          <span className={`font-medium ${isConnected ? 'text-success' : 'text-danger'}`}>
                            {isConnected ? 'Connected' : 'Disconnected'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Tab>
            </Tabs>
          </CardBody>
        </Card>
      </motion.div>
    );
  };

  if (!isVisible) return null;

  // Show loading state while fetching data
  if (teamsLoading || projectsLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <Card className="w-80 shadow-lg">
          <CardBody className="p-6 text-center">
            <div className="flex items-center justify-center gap-2">
              <Users size={18} className="text-primary animate-pulse" />
              <span className="text-sm">
                Loading {teamsLoading && projectsLoading ? 'teams & projects' : teamsLoading ? 'teams' : 'projects'}...
              </span>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    );
  }

  // Show message when no teams or projects available
  if (userTeams.length === 0 && userProjects.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <Card className="w-80 shadow-lg">
          <CardBody className="p-6 text-center">
            <div className="flex flex-col items-center gap-3">
              <Users size={24} className="text-default-400" />
              <div>
                <h4 className="font-semibold text-default-600">No Collaboration Context</h4>
                <p className="text-sm text-default-500 mt-1">
                  Join a team or create a project to use collaboration features
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    );
  }

  return (
    <AnimatePresence>
      {isExpanded ? renderExpandedView() : renderCompactView()}
    </AnimatePresence>
  );
};

export default RealtimeCollaborationHub;
