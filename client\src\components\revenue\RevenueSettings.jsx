// RevenueSettings - Comprehensive revenue configuration and preferences
// Implements revenue settings following dashboard specifications
import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Switch, Select, SelectItem, Input, Divider, Badge } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import TellerLinkComponent from '../payments/TellerLinkComponent';
import {
  Settings,
  DollarSign,
  Bell,
  Shield,
  Wallet,
  Target,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  CreditCard,
  Building2
} from 'lucide-react';

const RevenueSettings = ({ className = "", projectId = null }) => {
  const { currentUser } = useContext(UserContext);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [showProjectBankLink, setShowProjectBankLink] = useState(false);
  const [settings, setSettings] = useState({
    payment: {
      preferredMethod: 'bank_transfer',
      minimumPayout: 100,
      autoWithdraw: false,
      autoWithdrawThreshold: 500,
      currency: 'USD',
      taxWithholding: false,
      taxRate: 0
    },
    notifications: {
      paymentReceived: true,
      payoutProcessed: true,
      goalAchieved: true,
      weeklyReports: true,
      monthlyReports: true,
      thresholdAlerts: true,
      thresholdAmount: 1000
    },
    privacy: {
      showEarnings: false,
      showGoals: true,
      showRanking: true,
      publicProfile: false,
      shareAnalytics: true
    },
    goals: {
      monthlyTarget: 4000,
      yearlyTarget: 48000,
      orbTarget: 5000,
      trackProgress: true,
      reminderFrequency: 'weekly'
    },
    orb: {
      autoConvert: false,
      conversionThreshold: 1000,
      conversionPercentage: 50,
      stakingEnabled: false,
      stakingAmount: 0
    }
  });

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      
      // Simulate API call to save settings
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      alert('Settings saved successfully!');
      
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      setSettings({
        payment: {
          preferredMethod: 'bank_transfer',
          minimumPayout: 100,
          autoWithdraw: false,
          autoWithdrawThreshold: 500,
          currency: 'USD',
          taxWithholding: false,
          taxRate: 0
        },
        notifications: {
          paymentReceived: true,
          payoutProcessed: true,
          goalAchieved: true,
          weeklyReports: true,
          monthlyReports: true,
          thresholdAlerts: true,
          thresholdAmount: 1000
        },
        privacy: {
          showEarnings: false,
          showGoals: true,
          showRanking: true,
          publicProfile: false,
          shareAnalytics: true
        },
        goals: {
          monthlyTarget: 4000,
          yearlyTarget: 48000,
          orbTarget: 5000,
          trackProgress: true,
          reminderFrequency: 'weekly'
        },
        orb: {
          autoConvert: false,
          conversionThreshold: 1000,
          conversionPercentage: 50,
          stakingEnabled: false,
          stakingAmount: 0
        }
      });
      setHasChanges(true);
    }
  };

  return (
    <div className={`revenue-settings space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="text-gray-600" size={28} />
            Revenue Settings
          </h2>
          <p className="text-gray-600">Configure your revenue preferences and payment settings</p>
        </div>
        
        {hasChanges && (
          <Badge color="warning" variant="flat">
            Unsaved Changes
          </Badge>
        )}
      </div>

      {/* Payment Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <CreditCard className="text-green-500" size={20} />
            <h3 className="text-lg font-semibold">Payment & Withdrawal</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Preferred Payment Method"
                selectedKeys={[settings.payment.preferredMethod]}
                onSelectionChange={(keys) => handleSettingChange('payment', 'preferredMethod', Array.from(keys)[0])}
              >
                <SelectItem key="bank_transfer" startContent={<Building2 size={16} />}>
                  Bank Transfer
                </SelectItem>
                <SelectItem key="paypal" startContent={<Wallet size={16} />}>
                  PayPal
                </SelectItem>
                <SelectItem key="crypto" startContent={<DollarSign size={16} />}>
                  Cryptocurrency
                </SelectItem>
              </Select>
              
              <Select
                label="Currency"
                selectedKeys={[settings.payment.currency]}
                onSelectionChange={(keys) => handleSettingChange('payment', 'currency', Array.from(keys)[0])}
              >
                <SelectItem key="USD">USD ($)</SelectItem>
                <SelectItem key="EUR">EUR (€)</SelectItem>
                <SelectItem key="GBP">GBP (£)</SelectItem>
                <SelectItem key="CAD">CAD ($)</SelectItem>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Minimum Payout Amount"
                type="number"
                value={settings.payment.minimumPayout.toString()}
                onChange={(e) => handleSettingChange('payment', 'minimumPayout', parseFloat(e.target.value) || 0)}
                startContent="$"
                description="Minimum amount before payout is processed"
              />
              
              <Input
                label="Auto-Withdraw Threshold"
                type="number"
                value={settings.payment.autoWithdrawThreshold.toString()}
                onChange={(e) => handleSettingChange('payment', 'autoWithdrawThreshold', parseFloat(e.target.value) || 0)}
                startContent="$"
                description="Automatically withdraw when balance reaches this amount"
                isDisabled={!settings.payment.autoWithdraw}
              />
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Auto-Withdraw</h4>
                <p className="text-sm text-gray-600">Automatically withdraw funds when threshold is reached</p>
              </div>
              <Switch
                isSelected={settings.payment.autoWithdraw}
                onChange={(value) => handleSettingChange('payment', 'autoWithdraw', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Tax Withholding</h4>
                <p className="text-sm text-gray-600">Automatically withhold taxes from payments</p>
              </div>
              <Switch
                isSelected={settings.payment.taxWithholding}
                onChange={(value) => handleSettingChange('payment', 'taxWithholding', value)}
              />
            </div>
            
            {settings.payment.taxWithholding && (
              <Input
                label="Tax Rate (%)"
                type="number"
                value={settings.payment.taxRate.toString()}
                onChange={(e) => handleSettingChange('payment', 'taxRate', parseFloat(e.target.value) || 0)}
                endContent="%"
                max="50"
                min="0"
              />
            )}
          </div>
        </CardBody>
      </Card>

      {/* Project Bank Account Settings - Only show if projectId is provided */}
      {projectId && (
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Building2 className="text-purple-500" size={20} />
              <h3 className="text-lg font-semibold">Project Revenue Account</h3>
            </div>
          </CardHeader>
          <CardBody className="p-6">
            <div className="space-y-4">
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Automatic Revenue Monitoring</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Connect a bank account to automatically monitor project revenue and trigger distribution notifications.
                </p>
                <Button
                  color="primary"
                  variant="flat"
                  startContent={<Building2 size={16} />}
                  onPress={() => setShowProjectBankLink(true)}
                >
                  Connect Project Bank Account
                </Button>
              </div>

              <div className="text-sm text-gray-500">
                <p><strong>How it works:</strong></p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Incoming transactions are automatically detected</li>
                  <li>Revenue entries are created for manual approval</li>
                  <li>Contributors are notified when distribution is ready</li>
                  <li>Payments are distributed based on project agreements</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Bell className="text-blue-500" size={20} />
            <h3 className="text-lg font-semibold">Notifications</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Payment Received</h4>
                <p className="text-sm text-gray-600">Notify when payments are received</p>
              </div>
              <Switch
                isSelected={settings.notifications.paymentReceived}
                onChange={(value) => handleSettingChange('notifications', 'paymentReceived', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Payout Processed</h4>
                <p className="text-sm text-gray-600">Notify when payouts are processed</p>
              </div>
              <Switch
                isSelected={settings.notifications.payoutProcessed}
                onChange={(value) => handleSettingChange('notifications', 'payoutProcessed', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Goal Achieved</h4>
                <p className="text-sm text-gray-600">Notify when revenue goals are achieved</p>
              </div>
              <Switch
                isSelected={settings.notifications.goalAchieved}
                onChange={(value) => handleSettingChange('notifications', 'goalAchieved', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Weekly Reports</h4>
                <p className="text-sm text-gray-600">Receive weekly revenue summaries</p>
              </div>
              <Switch
                isSelected={settings.notifications.weeklyReports}
                onChange={(value) => handleSettingChange('notifications', 'weeklyReports', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Monthly Reports</h4>
                <p className="text-sm text-gray-600">Receive detailed monthly reports</p>
              </div>
              <Switch
                isSelected={settings.notifications.monthlyReports}
                onChange={(value) => handleSettingChange('notifications', 'monthlyReports', value)}
              />
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Threshold Alerts</h4>
                <p className="text-sm text-gray-600">Alert when earnings reach specified amount</p>
              </div>
              <Switch
                isSelected={settings.notifications.thresholdAlerts}
                onChange={(value) => handleSettingChange('notifications', 'thresholdAlerts', value)}
              />
            </div>
            
            {settings.notifications.thresholdAlerts && (
              <Input
                label="Alert Threshold"
                type="number"
                value={settings.notifications.thresholdAmount.toString()}
                onChange={(e) => handleSettingChange('notifications', 'thresholdAmount', parseFloat(e.target.value) || 0)}
                startContent="$"
              />
            )}
          </div>
        </CardBody>
      </Card>

      {/* Privacy Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Shield className="text-purple-500" size={20} />
            <h3 className="text-lg font-semibold">Privacy & Visibility</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Show Earnings</h4>
                <p className="text-sm text-gray-600">Display earnings on public profile</p>
              </div>
              <Switch
                isSelected={settings.privacy.showEarnings}
                onChange={(value) => handleSettingChange('privacy', 'showEarnings', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Show Goals</h4>
                <p className="text-sm text-gray-600">Display revenue goals publicly</p>
              </div>
              <Switch
                isSelected={settings.privacy.showGoals}
                onChange={(value) => handleSettingChange('privacy', 'showGoals', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Show Ranking</h4>
                <p className="text-sm text-gray-600">Display global ranking on profile</p>
              </div>
              <Switch
                isSelected={settings.privacy.showRanking}
                onChange={(value) => handleSettingChange('privacy', 'showRanking', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Public Profile</h4>
                <p className="text-sm text-gray-600">Make revenue dashboard publicly viewable</p>
              </div>
              <Switch
                isSelected={settings.privacy.publicProfile}
                onChange={(value) => handleSettingChange('privacy', 'publicProfile', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Share Analytics</h4>
                <p className="text-sm text-gray-600">Allow platform to use anonymized data for insights</p>
              </div>
              <Switch
                isSelected={settings.privacy.shareAnalytics}
                onChange={(value) => handleSettingChange('privacy', 'shareAnalytics', value)}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Goals Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Target className="text-orange-500" size={20} />
            <h3 className="text-lg font-semibold">Revenue Goals</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                label="Monthly Target"
                type="number"
                value={settings.goals.monthlyTarget.toString()}
                onChange={(e) => handleSettingChange('goals', 'monthlyTarget', parseFloat(e.target.value) || 0)}
                startContent="$"
              />
              
              <Input
                label="Yearly Target"
                type="number"
                value={settings.goals.yearlyTarget.toString()}
                onChange={(e) => handleSettingChange('goals', 'yearlyTarget', parseFloat(e.target.value) || 0)}
                startContent="$"
              />
              
              <Input
                label="ORB Target"
                type="number"
                value={settings.goals.orbTarget.toString()}
                onChange={(e) => handleSettingChange('goals', 'orbTarget', parseFloat(e.target.value) || 0)}
                endContent="ORB"
              />
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Track Progress</h4>
                <p className="text-sm text-gray-600">Enable goal progress tracking and analytics</p>
              </div>
              <Switch
                isSelected={settings.goals.trackProgress}
                onChange={(value) => handleSettingChange('goals', 'trackProgress', value)}
              />
            </div>
            
            <Select
              label="Reminder Frequency"
              selectedKeys={[settings.goals.reminderFrequency]}
              onSelectionChange={(keys) => handleSettingChange('goals', 'reminderFrequency', Array.from(keys)[0])}
              isDisabled={!settings.goals.trackProgress}
            >
              <SelectItem key="daily">Daily</SelectItem>
              <SelectItem key="weekly">Weekly</SelectItem>
              <SelectItem key="monthly">Monthly</SelectItem>
              <SelectItem key="never">Never</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="light"
          color="danger"
          startContent={<RefreshCw size={18} />}
          onPress={handleResetSettings}
        >
          Reset to Defaults
        </Button>
        
        <div className="flex gap-3">
          <Button
            variant="light"
            onPress={() => setHasChanges(false)}
            isDisabled={!hasChanges}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            startContent={<Save size={18} />}
            onPress={handleSaveSettings}
            isLoading={isSaving}
            isDisabled={!hasChanges}
          >
            Save Settings
          </Button>
        </div>
      </div>

      {/* Project Bank Account Link Modal */}
      {showProjectBankLink && (
        <TellerLinkComponent
          isOpen={showProjectBankLink}
          onClose={() => setShowProjectBankLink(false)}
          context="project"
          projectId={projectId}
          onSuccess={(data) => {
            console.log('Project bank account connected:', data);
            setShowProjectBankLink(false);
            // Could refresh project bank account data here
          }}
        />
      )}
    </div>
  );
};

export default RevenueSettings;
