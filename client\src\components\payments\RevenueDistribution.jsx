import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Avatar, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * RevenueDistribution Component - Revenue Sharing and Distribution Management
 * 
 * Features:
 * - Revenue distribution configuration
 * - Percentage-based and fixed amount sharing
 * - Automated payment scheduling
 * - Real-time distribution tracking
 * - Multi-contributor support
 */
const RevenueDistribution = ({ projectId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [distributions, setDistributions] = useState([]);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [selectedDistribution, setSelectedDistribution] = useState(null);
  
  // Configuration state
  const [totalRevenue, setTotalRevenue] = useState('');
  const [distributionModel, setDistributionModel] = useState('percentage');
  const [contributors, setContributors] = useState([
    { id: 1, name: '', email: '', percentage: 0, amount: 0, role: '' }
  ]);

  useEffect(() => {
    if (currentUser && projectId) {
      fetchDistributions();
    }
  }, [currentUser, projectId]);

  // Fetch revenue distributions
  const fetchDistributions = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/financial-transactions?project_id=${projectId}&type=revenue_distribution`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDistributions(data.distributions || []);
      } else {
        throw new Error('Failed to fetch revenue distributions');
      }
    } catch (error) {
      console.error('Error fetching distributions:', error);
      toast.error('Failed to load revenue distributions');
    } finally {
      setLoading(false);
    }
  };

  // Add contributor
  const addContributor = () => {
    setContributors([
      ...contributors,
      { id: Date.now(), name: '', email: '', percentage: 0, amount: 0, role: '' }
    ]);
  };

  // Remove contributor
  const removeContributor = (id) => {
    setContributors(contributors.filter(c => c.id !== id));
  };

  // Update contributor
  const updateContributor = (id, field, value) => {
    setContributors(contributors.map(c => 
      c.id === id ? { ...c, [field]: value } : c
    ));
  };

  // Calculate amounts based on percentages
  const calculateAmounts = () => {
    const revenue = parseFloat(totalRevenue) || 0;
    return contributors.map(c => ({
      ...c,
      amount: distributionModel === 'percentage' 
        ? (revenue * (parseFloat(c.percentage) || 0)) / 100
        : parseFloat(c.amount) || 0
    }));
  };

  // Get total percentage
  const getTotalPercentage = () => {
    return contributors.reduce((sum, c) => sum + (parseFloat(c.percentage) || 0), 0);
  };

  // Get total amount
  const getTotalAmount = () => {
    const calculatedContributors = calculateAmounts();
    return calculatedContributors.reduce((sum, c) => sum + c.amount, 0);
  };

  // Save distribution configuration
  const saveDistribution = async () => {
    try {
      const revenue = parseFloat(totalRevenue);
      if (!revenue || revenue <= 0) {
        toast.error('Please enter a valid total revenue amount');
        return;
      }

      if (distributionModel === 'percentage' && getTotalPercentage() !== 100) {
        toast.error('Total percentage must equal 100%');
        return;
      }

      const validContributors = contributors.filter(c => c.name && c.email);
      if (validContributors.length === 0) {
        toast.error('Please add at least one contributor');
        return;
      }

      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const distributionData = {
        project_id: projectId,
        total_amount: revenue,
        distribution_model: distributionModel,
        contributors: calculateAmounts().filter(c => c.name && c.email),
        created_by: currentUser.id
      };

      const response = await fetch('/.netlify/functions/financial-transactions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'create_revenue_distribution',
          ...distributionData
        })
      });

      if (response.ok) {
        toast.success('Revenue distribution configured successfully!');
        setShowConfigModal(false);
        await fetchDistributions();
        
        // Reset form
        setTotalRevenue('');
        setContributors([{ id: 1, name: '', email: '', percentage: 0, amount: 0, role: '' }]);
      } else {
        throw new Error('Failed to save revenue distribution');
      }
    } catch (error) {
      console.error('Error saving distribution:', error);
      toast.error('Failed to save revenue distribution');
    }
  };

  // Process distribution - Execute actual transfers to contributors' personal accounts
  const processDistribution = async (distributionId) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Step 1: Update distribution status to processing
      const statusResponse = await fetch('/.netlify/functions/financial-transactions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'process_revenue_distribution',
          distribution_id: distributionId,
          user_id: currentUser.id
        })
      });

      if (!statusResponse.ok) {
        throw new Error('Failed to update distribution status');
      }

      // Step 2: Execute the actual transfers from project account to contributors' personal accounts
      const transferResponse = await fetch('/.netlify/functions/teller-payments', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'execute_revenue_distribution',
          distribution_id: distributionId,
          project_id: projectId
        })
      });

      if (!transferResponse.ok) {
        const transferError = await transferResponse.json();
        throw new Error(transferError.error || 'Failed to execute transfers');
      }

      const transferResult = await transferResponse.json();
      console.log('Revenue distribution transfers executed:', transferResult);

      toast.success(`Revenue distribution processed! ${transferResult.successful_transfers || 0} transfers completed.`);
      await fetchDistributions();

    } catch (error) {
      console.error('Error processing distribution:', error);
      toast.error(`Failed to process revenue distribution: ${error.message}`);
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      failed: 'danger'
    };
    return colors[status] || 'default';
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading revenue distributions...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`revenue-distribution ${className}`}>
      <Card className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 border-2 border-green-200 dark:border-green-700">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💰</span>
              <h3 className="text-lg font-semibold">Revenue Distribution</h3>
            </div>
            <Button
              color="success"
              size="sm"
              onPress={() => setShowConfigModal(true)}
            >
              + Configure Distribution
            </Button>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {distributions.length === 0 ? (
            <div className="text-center py-8">
              <span className="text-4xl mb-4 block">📊</span>
              <h4 className="text-lg font-medium mb-2">No Revenue Distributions</h4>
              <p className="text-default-600 mb-4">
                Configure revenue sharing to automatically distribute payments to contributors
              </p>
              <Button
                color="success"
                onPress={() => setShowConfigModal(true)}
              >
                Configure First Distribution
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {distributions.map((distribution, index) => (
                <motion.div
                  key={distribution.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="border border-default-200">
                    <CardBody className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-lg">
                            {formatCurrency(distribution.total_amount)} Distribution
                          </h4>
                          <p className="text-default-600 text-sm">
                            {distribution.contributors?.length || 0} contributors • 
                            Created {formatDate(distribution.created_at)}
                          </p>
                        </div>
                        <Chip 
                          color={getStatusColor(distribution.status)} 
                          variant="flat" 
                          size="sm"
                        >
                          {distribution.status}
                        </Chip>
                      </div>

                      {/* Contributors List */}
                      <div className="space-y-2 mb-4">
                        {distribution.contributors?.map((contributor, idx) => (
                          <div key={idx} className="flex items-center justify-between p-2 bg-default-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Avatar 
                                name={contributor.name}
                                size="sm"
                              />
                              <div>
                                <div className="font-medium text-sm">{contributor.name}</div>
                                <div className="text-xs text-default-500">{contributor.role}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">{formatCurrency(contributor.amount)}</div>
                              <div className="text-xs text-default-500">
                                {distribution.distribution_model === 'percentage' ? `${contributor.percentage}%` : 'Fixed'}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Progress */}
                      {distribution.status === 'processing' && (
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Distribution Progress</span>
                            <span className="text-sm text-default-600">
                              {distribution.completed_payments || 0} / {distribution.contributors?.length || 0}
                            </span>
                          </div>
                          <Progress 
                            value={((distribution.completed_payments || 0) / (distribution.contributors?.length || 1)) * 100} 
                            color="success" 
                            size="sm"
                          />
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-2">
                        {distribution.status === 'pending' && (
                          <Button
                            color="success"
                            size="sm"
                            onPress={() => processDistribution(distribution.id)}
                          >
                            💸 Process Distribution
                          </Button>
                        )}
                        <Button
                          variant="light"
                          size="sm"
                          onPress={() => setSelectedDistribution(distribution)}
                        >
                          📄 View Details
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Configuration Modal */}
      <Modal 
        isOpen={showConfigModal} 
        onClose={() => setShowConfigModal(false)}
        size="4xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <span className="text-xl">💰 Configure Revenue Distribution</span>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              {/* Basic Configuration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Total Revenue"
                  placeholder="10000.00"
                  startContent="$"
                  value={totalRevenue}
                  onChange={(e) => setTotalRevenue(e.target.value)}
                  type="number"
                />
                <Select
                  label="Distribution Model"
                  selectedKeys={[distributionModel]}
                  onSelectionChange={(keys) => setDistributionModel(Array.from(keys)[0])}
                >
                  <SelectItem key="percentage">Percentage-based</SelectItem>
                  <SelectItem key="fixed">Fixed amounts</SelectItem>
                </Select>
              </div>

              {/* Contributors */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold">Contributors</h3>
                  <Button size="sm" onPress={addContributor}>
                    + Add Contributor
                  </Button>
                </div>
                
                <div className="space-y-3">
                  {contributors.map((contributor, index) => (
                    <Card key={contributor.id} className="border border-default-200">
                      <CardBody className="p-4">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                          <Input
                            label="Name"
                            placeholder="John Doe"
                            value={contributor.name}
                            onChange={(e) => updateContributor(contributor.id, 'name', e.target.value)}
                          />
                          <Input
                            label="Email"
                            placeholder="<EMAIL>"
                            value={contributor.email}
                            onChange={(e) => updateContributor(contributor.id, 'email', e.target.value)}
                          />
                          <Input
                            label="Role"
                            placeholder="Lead Developer"
                            value={contributor.role}
                            onChange={(e) => updateContributor(contributor.id, 'role', e.target.value)}
                          />
                          <div className="flex gap-2">
                            <Input
                              label={distributionModel === 'percentage' ? 'Percentage' : 'Amount'}
                              placeholder={distributionModel === 'percentage' ? '25' : '2500.00'}
                              endContent={distributionModel === 'percentage' ? '%' : '$'}
                              value={distributionModel === 'percentage' ? contributor.percentage : contributor.amount}
                              onChange={(e) => updateContributor(
                                contributor.id, 
                                distributionModel === 'percentage' ? 'percentage' : 'amount', 
                                e.target.value
                              )}
                              type="number"
                            />
                            {contributors.length > 1 && (
                              <Button
                                color="danger"
                                variant="light"
                                size="sm"
                                onPress={() => removeContributor(contributor.id)}
                                className="mt-6"
                              >
                                ❌
                              </Button>
                            )}
                          </div>
                        </div>
                        
                        {distributionModel === 'percentage' && totalRevenue && (
                          <div className="mt-2 text-sm text-default-600">
                            Amount: {formatCurrency((parseFloat(totalRevenue) * (parseFloat(contributor.percentage) || 0)) / 100)}
                          </div>
                        )}
                      </CardBody>
                    </Card>
                  ))}
                </div>

                {/* Summary */}
                <Card className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 mt-4">
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold">Distribution Summary</h4>
                        <p className="text-sm text-default-600">
                          {distributionModel === 'percentage' 
                            ? `Total: ${getTotalPercentage()}% (${getTotalPercentage() === 100 ? 'Complete' : 'Incomplete'})`
                            : `Total: ${formatCurrency(getTotalAmount())}`
                          }
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">
                          {formatCurrency(getTotalAmount())}
                        </div>
                        <div className="text-sm text-default-600">
                          {contributors.filter(c => c.name && c.email).length} contributors
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowConfigModal(false)}>
              Cancel
            </Button>
            <Button 
              color="success" 
              onPress={saveDistribution}
              isDisabled={
                !totalRevenue || 
                (distributionModel === 'percentage' && getTotalPercentage() !== 100) ||
                contributors.filter(c => c.name && c.email).length === 0
              }
            >
              Save Distribution
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default RevenueDistribution;
