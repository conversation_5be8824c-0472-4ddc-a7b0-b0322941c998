// Intelligent Gigwork Matching System
// AI-powered matching algorithm for optimal candidate-project pairings

import { supabase } from '../supabase/supabase.utils';
import { getUserVettingData, getEnhancedUserProfile } from '../profile/enhancedProfile.utils';

/**
 * Calculate comprehensive match score between user and gigwork request
 * @param {string} userId - The user ID
 * @param {Object} request - The gigwork request object
 * @returns {Promise<Object>} Detailed match analysis with score and breakdown
 */
export const calculateMatchScore = async (userId, request) => {
  try {
    // Get user data
    const [userProfile, vettingData] = await Promise.all([
      getEnhancedUserProfile(userId),
      getUserVettingData(userId)
    ]);

    // Calculate individual match components
    const skillMatch = await calculateSkillMatch(vettingData, request);
    const vettingMatch = calculateVettingMatch(vettingData, request);
    const experienceMatch = calculateExperienceMatch(userProfile, request);
    const availabilityMatch = calculateAvailabilityMatch(userProfile, request);
    const budgetMatch = calculateBudgetMatch(userProfile, request);
    const locationMatch = calculateLocationMatch(userProfile, request);
    const successRateMatch = await calculateSuccessRateMatch(userId, request);

    // Weighted scoring system
    const weights = {
      skills: 0.30,        // 30% - Most important
      vetting: 0.20,       // 20% - Professional verification
      experience: 0.15,    // 15% - Years of experience
      availability: 0.10,  // 10% - Current availability
      budget: 0.10,        // 10% - Budget alignment
      location: 0.05,      // 5% - Location preference
      successRate: 0.10    // 10% - Historical success
    };

    // Calculate weighted total score
    const totalScore = Math.round(
      (skillMatch.score * weights.skills) +
      (vettingMatch.score * weights.vetting) +
      (experienceMatch.score * weights.experience) +
      (availabilityMatch.score * weights.availability) +
      (budgetMatch.score * weights.budget) +
      (locationMatch.score * weights.location) +
      (successRateMatch.score * weights.successRate)
    );

    return {
      totalScore,
      breakdown: {
        skills: skillMatch,
        vetting: vettingMatch,
        experience: experienceMatch,
        availability: availabilityMatch,
        budget: budgetMatch,
        location: locationMatch,
        successRate: successRateMatch
      },
      recommendation: generateRecommendation(totalScore, {
        skillMatch,
        vettingMatch,
        experienceMatch
      }),
      matchLevel: getMatchLevel(totalScore)
    };
  } catch (error) {
    console.error('Error calculating match score:', error);
    return {
      totalScore: 0,
      breakdown: {},
      recommendation: 'Unable to calculate match score',
      matchLevel: 'unknown'
    };
  }
};

/**
 * Calculate skill match score based on required skills vs user skills
 */
const calculateSkillMatch = async (vettingData, request) => {
  if (!request.required_skills || request.required_skills.length === 0) {
    return { score: 50, details: 'No specific skills required', matches: [] };
  }

  const skillMatches = request.required_skills.map(requiredSkill => {
    const userSkill = vettingData.skillLevels.find(
      skill => skill.technology.toLowerCase() === requiredSkill.toLowerCase()
    );

    const userLevel = userSkill?.current_level || 0;
    const requiredLevel = 1; // Default minimum level
    const meetsRequirement = userLevel >= requiredLevel;

    return {
      skill: requiredSkill,
      userLevel,
      requiredLevel,
      meetsRequirement,
      score: meetsRequirement ? Math.min(100, (userLevel / requiredLevel) * 100) : 0
    };
  });

  const totalMatches = skillMatches.filter(match => match.meetsRequirement).length;
  const score = Math.round((totalMatches / request.required_skills.length) * 100);

  return {
    score,
    details: `${totalMatches}/${request.required_skills.length} skills matched`,
    matches: skillMatches
  };
};

/**
 * Calculate vetting level match score
 */
const calculateVettingMatch = (vettingData, request) => {
  const userVettingLevel = vettingData.overallVettingLevel || 0;
  const minRequired = request.minimum_vetting_level || 0;
  const preferred = request.preferred_vetting_level || minRequired;

  if (userVettingLevel >= preferred) {
    return {
      score: 100,
      details: 'Exceeds preferred vetting level',
      userLevel: userVettingLevel,
      requiredLevel: minRequired,
      preferredLevel: preferred
    };
  } else if (userVettingLevel >= minRequired) {
    const score = Math.round(70 + ((userVettingLevel - minRequired) / (preferred - minRequired)) * 30);
    return {
      score,
      details: 'Meets minimum vetting requirements',
      userLevel: userVettingLevel,
      requiredLevel: minRequired,
      preferredLevel: preferred
    };
  } else {
    return {
      score: Math.round((userVettingLevel / minRequired) * 50),
      details: 'Below minimum vetting level',
      userLevel: userVettingLevel,
      requiredLevel: minRequired,
      preferredLevel: preferred
    };
  }
};

/**
 * Calculate experience level match
 */
const calculateExperienceMatch = (userProfile, request) => {
  const userExperience = userProfile.years_experience || 0;
  const requiredExperience = getExperienceYears(request.experience_level);

  if (userExperience >= requiredExperience * 1.5) {
    return { score: 100, details: 'Highly experienced' };
  } else if (userExperience >= requiredExperience) {
    return { score: 85, details: 'Meets experience requirements' };
  } else if (userExperience >= requiredExperience * 0.7) {
    return { score: 60, details: 'Slightly below experience requirements' };
  } else {
    return { score: 30, details: 'Limited experience for this role' };
  }
};

/**
 * Calculate availability match
 */
const calculateAvailabilityMatch = (userProfile, request) => {
  const userAvailability = userProfile.availability_status || 'available';
  
  switch (userAvailability) {
    case 'available':
      return { score: 100, details: 'Fully available' };
    case 'busy':
      return { score: 60, details: 'Limited availability' };
    case 'unavailable':
      return { score: 20, details: 'Currently unavailable' };
    default:
      return { score: 50, details: 'Availability unknown' };
  }
};

/**
 * Calculate budget alignment match
 */
const calculateBudgetMatch = (userProfile, request) => {
  const userRate = userProfile.hourly_rate;
  const projectBudgetMin = request.budget_range_min || 0;
  const projectBudgetMax = request.budget_range_max || 0;
  const timelineWeeks = request.timeline_weeks || 4;

  if (!userRate || (!projectBudgetMin && !projectBudgetMax)) {
    return { score: 50, details: 'Budget information incomplete' };
  }

  // Estimate project hours (assuming 20-40 hours per week)
  const estimatedHours = timelineWeeks * 30; // Average 30 hours per week
  const userProjectCost = userRate * estimatedHours;

  if (projectBudgetMax === 0) {
    return { score: 75, details: 'Open budget - likely negotiable' };
  }

  if (userProjectCost <= projectBudgetMax && userProjectCost >= projectBudgetMin) {
    return { score: 100, details: 'Perfect budget alignment' };
  } else if (userProjectCost <= projectBudgetMax * 1.2) {
    return { score: 80, details: 'Close budget match' };
  } else if (userProjectCost >= projectBudgetMin * 0.8) {
    return { score: 60, details: 'Reasonable budget range' };
  } else {
    return { score: 30, details: 'Budget mismatch' };
  }
};

/**
 * Calculate location preference match
 */
const calculateLocationMatch = (userProfile, request) => {
  // For now, assume remote work is preferred
  // This can be enhanced with actual location preferences
  return { score: 80, details: 'Remote work assumed' };
};

/**
 * Calculate success rate based on historical performance
 */
const calculateSuccessRateMatch = async (userId, request) => {
  try {
    // Get user's application history
    const { data: applications, error } = await supabase
      .from('collaboration_request_applications')
      .select('status, applied_at')
      .eq('applicant_id', userId);

    if (error || !applications || applications.length === 0) {
      return { score: 50, details: 'No application history' };
    }

    const totalApplications = applications.length;
    const acceptedApplications = applications.filter(app => app.status === 'accepted').length;
    const successRate = (acceptedApplications / totalApplications) * 100;

    return {
      score: Math.min(100, successRate + 20), // Boost score slightly
      details: `${acceptedApplications}/${totalApplications} applications accepted`,
      successRate: Math.round(successRate)
    };
  } catch (error) {
    return { score: 50, details: 'Unable to calculate success rate' };
  }
};

/**
 * Helper function to convert experience level to years
 */
const getExperienceYears = (level) => {
  switch (level) {
    case 'beginner': return 0;
    case 'intermediate': return 2;
    case 'advanced': return 5;
    case 'expert': return 8;
    default: return 1;
  }
};

/**
 * Generate recommendation based on match score and breakdown
 */
const generateRecommendation = (totalScore, breakdown) => {
  if (totalScore >= 85) {
    return 'Excellent match! Highly recommended to apply.';
  } else if (totalScore >= 70) {
    return 'Good match. Consider applying with a strong proposal.';
  } else if (totalScore >= 50) {
    return 'Moderate match. Review requirements carefully before applying.';
  } else {
    return 'Limited match. Consider improving skills or vetting level first.';
  }
};

/**
 * Get match level category
 */
const getMatchLevel = (score) => {
  if (score >= 85) return 'excellent';
  if (score >= 70) return 'good';
  if (score >= 50) return 'moderate';
  return 'limited';
};

/**
 * Get intelligent recommendations for a user using the API endpoint
 * @param {string} userId - The user ID
 * @param {Array} requests - Array of gigwork requests
 * @param {Object} options - Filtering and sorting options
 * @returns {Promise<Array>} Sorted and filtered requests with match scores
 */
export const getIntelligentRecommendations = async (userId, requests, options = {}) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      console.error('No authentication session available');
      return requests; // Return original requests if not authenticated
    }

    const response = await fetch('/.netlify/functions/intelligent-matching', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        requests,
        options
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API error:', errorData);
      return requests; // Fallback to original requests
    }

    const result = await response.json();

    if (result.success && (result.matches || result.recommendations)) {
      return result.matches || result.recommendations;
    } else {
      console.error('Invalid API response:', result);
      return requests; // Fallback to original requests
    }
  } catch (error) {
    console.error('Error getting intelligent recommendations:', error);
    return requests; // Return original requests if matching fails
  }
};

/**
 * Calculate match score for a single request (client-side fallback)
 * This is kept as a fallback for when the API is unavailable
 */
export const calculateMatchScoreFallback = async (userId, request) => {
  try {
    // Get user data
    const [userProfile, vettingData] = await Promise.all([
      getEnhancedUserProfile(userId),
      getUserVettingData(userId)
    ]);

    // Simple client-side scoring (reduced complexity)
    const skillMatch = await calculateSkillMatch(vettingData, request);
    const vettingMatch = calculateVettingMatch(vettingData, request);

    // Simplified scoring
    const totalScore = Math.round((skillMatch.score * 0.6) + (vettingMatch.score * 0.4));

    return {
      totalScore,
      breakdown: {
        skills: skillMatch,
        vetting: vettingMatch
      },
      recommendation: generateRecommendation(totalScore),
      matchLevel: getMatchLevel(totalScore)
    };
  } catch (error) {
    console.error('Error calculating fallback match score:', error);
    return {
      totalScore: null,
      breakdown: {},
      recommendation: 'Match scoring unavailable - insufficient data',
      matchLevel: 'unknown',
      error: 'Unable to calculate match score due to missing data'
    };
  }
};
