// TellerLinkComponent - Bank account linking interface
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Modal, 
  ModalContent, 
  ModalHeader, 
  ModalBody, 
  ModalFooter,
  Button,
  Card,
  CardBody,
  Chip,
  Divider
} from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Building2, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  CreditCard,
  Lock,
  Zap,
  Info
} from 'lucide-react';

const TellerLinkComponent = ({ isOpen, onClose, onSuccess, context = 'personal', projectId = null }) => {
  const { currentUser } = useContext(UserContext);
  const [linkToken, setLinkToken] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState('intro'); // 'intro', 'linking', 'success', 'error'
  const [error, setError] = useState(null);

  // Determine the context for bank linking
  const isProjectContext = context === 'project' && projectId;
  const contextTitle = isProjectContext ? 'Project Revenue Account' : 'Personal Bank Account';
  const contextDescription = isProjectContext
    ? 'This account will be monitored for project revenue and automatically trigger distribution notifications.'
    : 'This account will be used for your personal earnings, gigwork payments, and individual transactions.';

  // Handle Teller OAuth callback if this component is loaded in a popup
  useEffect(() => {
    const handlePopupCallback = () => {
      // Check if we're in a popup window with a callback
      if (window.opener && window.opener !== window) {
        const urlParams = new URLSearchParams(window.location.search);
        const tellerCallback = urlParams.get('teller_callback');
        const authCode = urlParams.get('code');
        const error = urlParams.get('error');

        if (tellerCallback === 'true') {
          console.log('🏦 TellerLinkComponent detected popup callback');

          // Send message to parent window
          if (error) {
            window.opener.postMessage({
              type: 'TELLER_ERROR',
              error: error
            }, window.location.origin);
          } else if (authCode) {
            window.opener.postMessage({
              type: 'TELLER_SUCCESS',
              code: authCode
            }, window.location.origin);
          }

          // Close the popup immediately
          window.close();
          return true; // Indicate callback was handled
        }
      }
      return false; // No callback handled
    };

    // Run callback handler immediately
    handlePopupCallback();
  }, []);

  useEffect(() => {
    if (isOpen && currentUser) {
      createLinkToken();
    }
  }, [isOpen, currentUser]);

  const createLinkToken = async () => {
    try {
      setIsLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (!authToken) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/.netlify/functions/teller-link/create-link-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          products: ['auth', 'transactions', 'transfer'],
          country_codes: ['US'],
          language: 'en',
          context: context,
          project_id: projectId
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || 'Bank linking service temporarily unavailable';
        throw new Error(errorMessage);
      }

      const data = await response.json();

      // Teller uses OAuth flow, so we get an authorization URL instead of a link token
      if (data.data?.authorization_url) {
        setLinkToken(data.data.authorization_url);
      } else {
        throw new Error('Invalid response from bank linking service');
      }

    } catch (error) {
      console.error('Error creating link token:', error);
      setError(error.message);
      setStep('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnectBank = async () => {
    setStep('linking');

    try {
      if (!linkToken) {
        throw new Error('Bank linking not initialized. Please try again.');
      }

      // For Teller OAuth flow, redirect to the authorization URL
      console.log('Redirecting to Teller OAuth:', linkToken);

      // Open Teller OAuth in a new window/tab
      const authWindow = window.open(
        linkToken,
        'teller-auth',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      if (!authWindow) {
        throw new Error('Please allow popups to connect your bank account');
      }

      // Listen for the OAuth callback
      const handleMessage = (event) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'TELLER_SUCCESS') {
          window.removeEventListener('message', handleMessage);
          authWindow.close();
          handleOAuthSuccess(event.data.code);
        } else if (event.data.type === 'TELLER_ERROR') {
          window.removeEventListener('message', handleMessage);
          authWindow.close();
          setError(event.data.error || 'Bank connection failed');
          setStep('error');
        }
      };

      window.addEventListener('message', handleMessage);

      // Check if window was closed manually
      const checkClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          setStep('intro');
          setIsLoading(false);
        }
      }, 1000);

    } catch (error) {
      console.error('Error connecting bank:', error);
      setError(error.message);
      setStep('error');
    }
  };

  const handleOAuthSuccess = async (authCode) => {
    try {
      setIsLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      const response = await fetch('/.netlify/functions/teller-link/exchange-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          authorization_code: authCode,
          context: context,
          project_id: projectId
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to complete bank connection');
      }

      const data = await response.json();

      setStep('success');

      // Call success callback with account data
      if (onSuccess) {
        onSuccess({
          access_token: data.data?.access_token,
          item_id: data.data?.item_id,
          accounts: data.data?.accounts
        });
      }

    } catch (error) {
      console.error('Error completing OAuth:', error);
      setError(error.message);
      setStep('error');
    } finally {
      setIsLoading(false);
    }
  };

  // Legacy function for backward compatibility
  const exchangePublicToken = async (publicToken) => {
    // This is now handled by handleOAuthSuccess
    console.warn('exchangePublicToken is deprecated, using OAuth flow');
  };

  const handleClose = () => {
    setStep('intro');
    setError(null);
    setLinkToken(null);
    onClose();
  };

  const renderIntroStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Shield className="text-blue-600" size={32} />
        </div>
        <h3 className="text-xl font-semibold mb-2">Connect {contextTitle}</h3>
        <p className="text-gray-600">
          {contextDescription}
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Lock className="text-green-500" size={20} />
            <div>
              <h4 className="font-medium text-sm">Bank-Level Security</h4>
              <p className="text-xs text-gray-600">Certificate-based auth</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Zap className="text-blue-500" size={20} />
            <div>
              <h4 className="font-medium text-sm">Instant Verification</h4>
              <p className="text-xs text-gray-600">Real-time connection</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="space-y-4">
        <Button
          color="primary"
          size="lg"
          className="w-full"
          startContent={<Building2 size={20} />}
          onPress={handleConnectBank}
          isLoading={isLoading}
        >
          Connect {isProjectContext ? 'Project Account' : 'Bank Account'}
        </Button>

        <p className="text-sm text-gray-600 text-center">
          You'll be redirected to securely connect your bank account through Teller
        </p>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <Info className="text-blue-500 flex-shrink-0 mt-0.5" size={16} />
          <div className="text-sm">
            <p className="font-medium text-blue-900">Powered by Teller</p>
            <p className="text-blue-700">
              Your credentials are encrypted and never stored on our servers. 
              Teller provides superior developer experience with certificate-based security.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLinkingStep = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <CreditCard className="text-blue-600" size={32} />
        </motion.div>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-2">Connecting Your Bank Account</h3>
        <p className="text-gray-600">
          Please wait while we securely connect your account...
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
        <p className="text-sm text-gray-500">Verifying account details...</p>
      </div>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="text-green-600" size={32} />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-2">Account Connected Successfully!</h3>
        <p className="text-gray-600">
          Your bank account has been securely linked to your Royaltea account.
        </p>
      </div>

      <Card className="bg-green-50 border-green-200">
        <CardBody className="p-4">
          <div className="flex items-center gap-3">
            <span className="text-2xl">🏦</span>
            <div className="text-left">
              <p className="font-medium">Bank Account</p>
              <p className="text-sm text-gray-600">Successfully Connected</p>
            </div>
            <Chip size="sm" color="success" variant="flat" className="ml-auto">
              Verified
            </Chip>
          </div>
        </CardBody>
      </Card>

      <div className="text-sm text-gray-600">
        <p>You can now:</p>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>Receive payments directly to your bank account</li>
          <li>Transfer funds with low fees</li>
          <li>View real-time transaction history</li>
        </ul>
      </div>
    </div>
  );

  const renderErrorStep = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
        <AlertCircle className="text-red-600" size={32} />
      </div>

      <div>
        <h3 className="text-xl font-semibold mb-2">Service Temporarily Unavailable</h3>
        <p className="text-gray-600 mb-4">
          Bank linking is currently being set up. Please check back later or contact support.
        </p>
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}
      </div>

      <div className="space-y-3">
        <Button
          color="primary"
          onPress={() => {
            setStep('intro');
            setError(null);
            createLinkToken();
          }}
        >
          Try Again
        </Button>

        <Button
          variant="light"
          onPress={handleClose}
        >
          Close
        </Button>
      </div>
    </div>
  );

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      scrollBehavior="inside"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <CreditCard size={20} className="text-primary" />
            <span>Add Payment Method</span>
          </div>
        </ModalHeader>
        
        <ModalBody className="py-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={step}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {step === 'intro' && renderIntroStep()}
              {step === 'linking' && renderLinkingStep()}
              {step === 'success' && renderSuccessStep()}
              {step === 'error' && renderErrorStep()}
            </motion.div>
          </AnimatePresence>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
            isDisabled={isLoading}
          >
            {step === 'success' ? 'Done' : 'Cancel'}
          </Button>
          {step === 'success' && (
            <Button 
              color="primary" 
              onPress={handleClose}
            >
              Continue
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default TellerLinkComponent;
