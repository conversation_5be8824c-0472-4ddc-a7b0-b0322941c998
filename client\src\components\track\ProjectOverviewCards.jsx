import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, Progress, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  FolderOpen, 
  CheckSquare, 
  TrendingUp, 
  Clock,
  Users,
  Target,
  Calendar,
  Activity
} from 'lucide-react';

/**
 * Project Overview Cards Component
 * 
 * Displays 4 key metrics cards:
 * - Active Projects (count + status)
 * - Tasks This Week (completed vs remaining)
 * - Team Velocity (percentage)
 * - Upcoming Deadlines (next deadline info)
 */
const ProjectOverviewCards = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [metrics, setMetrics] = useState({
    activeProjects: { count: 0, status: 'loading' },
    tasksThisWeek: { completed: 0, total: 0, percentage: 0 },
    teamVelocity: { percentage: 0, trend: 'stable' },
    upcomingDeadlines: { next: null, count: 0 }
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentUser) {
      loadMetrics();
    }
  }, [currentUser]);

  const loadMetrics = async () => {
    try {
      setLoading(true);

      // Get active projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('id, name, status, created_at')
        .eq('created_by', currentUser.id)
        .in('status', ['active', 'planning']);

      if (projectsError) throw projectsError;

      // Get tasks for this week with better error handling
      const startOfWeek = new Date();
      startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(endOfWeek.getDate() + 6);

      let tasks = [];
      try {
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select('id, status, created_at, due_date')
          .gte('created_at', startOfWeek.toISOString())
          .lte('created_at', endOfWeek.toISOString());

        if (tasksError) {
          console.warn('Tasks date range query failed:', tasksError);
          // Try a simpler query without date filtering
          const { data: allTasks, error: allTasksError } = await supabase
            .from('tasks')
            .select('id, status, created_at, due_date')
            .limit(50);

          if (!allTasksError) {
            tasks = allTasks || [];
          }
        } else {
          tasks = tasksData || [];
        }
      } catch (err) {
        console.warn('Tasks query failed completely:', err);
        tasks = [];
      }

      // Get upcoming deadlines
      const { data: deadlines, error: deadlinesError } = await supabase
        .from('tasks')
        .select('id, title, due_date, project_id')
        .not('due_date', 'is', null)
        .gte('due_date', new Date().toISOString())
        .order('due_date', { ascending: true })
        .limit(5);

      if (deadlinesError) throw deadlinesError;

      // Calculate metrics
      const completedTasks = tasks?.filter(task => task.status === 'done').length || 0;
      const totalTasks = tasks?.length || 0;
      const tasksPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

      // Calculate team velocity (simplified - based on task completion rate)
      const velocityPercentage = tasksPercentage;
      const velocityTrend = velocityPercentage >= 70 ? 'up' : velocityPercentage >= 40 ? 'stable' : 'down';

      setMetrics({
        activeProjects: {
          count: projects?.length || 0,
          status: projects?.length > 0 ? 'active' : 'none'
        },
        tasksThisWeek: {
          completed: completedTasks,
          total: totalTasks,
          percentage: tasksPercentage
        },
        teamVelocity: {
          percentage: velocityPercentage,
          trend: velocityTrend
        },
        upcomingDeadlines: {
          next: deadlines?.[0] || null,
          count: deadlines?.length || 0
        }
      });

    } catch (error) {
      console.error('Error loading metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 7) return `${diffDays} days`;
    return date.toLocaleDateString();
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'down': return <TrendingUp className="w-4 h-4 text-red-400 rotate-180" />;
      default: return <Activity className="w-4 h-4 text-yellow-400" />;
    }
  };

  const cards = [
    {
      id: 'active-projects',
      title: 'Active Projects',
      icon: FolderOpen,
      value: metrics.activeProjects.count,
      subtitle: metrics.activeProjects.status === 'active' ? 'In Progress' : 'No Active Projects',
      color: 'primary',
      bgGradient: 'from-blue-500/20 to-cyan-500/20'
    },
    {
      id: 'tasks-week',
      title: 'Tasks This Week',
      icon: CheckSquare,
      value: `${metrics.tasksThisWeek.completed}/${metrics.tasksThisWeek.total}`,
      subtitle: `${metrics.tasksThisWeek.percentage}% Complete`,
      color: 'success',
      bgGradient: 'from-green-500/20 to-emerald-500/20',
      progress: metrics.tasksThisWeek.percentage
    },
    {
      id: 'team-velocity',
      title: 'Team Velocity',
      icon: Users,
      value: `${metrics.teamVelocity.percentage}%`,
      subtitle: 'Completion Rate',
      color: 'warning',
      bgGradient: 'from-orange-500/20 to-yellow-500/20',
      trend: metrics.teamVelocity.trend
    },
    {
      id: 'upcoming-deadlines',
      title: 'Upcoming Deadlines',
      icon: Clock,
      value: metrics.upcomingDeadlines.count,
      subtitle: metrics.upcomingDeadlines.next 
        ? formatDate(metrics.upcomingDeadlines.next.due_date)
        : 'No deadlines',
      color: 'danger',
      bgGradient: 'from-red-500/20 to-pink-500/20'
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {cards.map((card, index) => (
        <motion.div
          key={card.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className={`bg-gradient-to-br ${card.bgGradient} border border-white/10 hover:border-white/20 transition-all duration-200`}>
            <CardBody className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg bg-${card.color}-500/20`}>
                    <card.icon className={`w-5 h-5 text-${card.color}-400`} />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-foreground/70">{card.title}</h3>
                  </div>
                </div>
                {card.trend && getTrendIcon(card.trend)}
              </div>

              <div className="space-y-2">
                <div className="text-2xl font-bold text-foreground">
                  {loading ? '...' : card.value}
                </div>
                <div className="text-sm text-foreground/60">
                  {card.subtitle}
                </div>
                
                {card.progress !== undefined && (
                  <Progress
                    value={card.progress}
                    color={card.color}
                    size="sm"
                    className="mt-3"
                  />
                )}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

export default ProjectOverviewCards;
