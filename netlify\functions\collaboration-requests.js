// Collaboration Requests API
// Handles gigwork and collaboration opportunity requests

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null; // Allow anonymous access for browsing
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return null;
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
};

// Get collaboration requests
const getCollaborationRequests = async (type = 'available', limit = 50, user = null) => {
  try {
    // First check if the table exists by doing a simple query
    const { data: testData, error: testError } = await supabase
      .from('collaboration_requests')
      .select('id')
      .limit(1);

    if (testError) {
      console.log('Collaboration requests table not available:', testError.message);
      // Return sample data if table doesn't exist
      return {
        requests: [
          {
            id: 'sample-1',
            title: 'Frontend Developer for React Dashboard',
            description: 'Looking for an experienced React developer to build a modern dashboard interface.',
            project_type: 'software',
            required_skills: ['React', 'JavaScript', 'CSS'],
            experience_level: 'intermediate',
            estimated_hours: 40,
            budget_min: 2000,
            budget_max: 3500,
            payment_type: 'fixed',
            is_remote: true,
            created_at: new Date().toISOString(),
            created_by_user: { display_name: 'Sample User', avatar_url: null }
          }
        ],
        total: 1
      };
    }

    let query = supabase
      .from('collaboration_requests')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    // Filter based on type
    switch (type) {
      case 'available':
        query = query.eq('status', 'open');
        break;
      case 'my-requests':
        if (user) {
          query = query.eq('created_by', user.id);
        } else {
          return { requests: [], total: 0 };
        }
        break;
      case 'applied':
        // For now, return empty array - would need applications table
        return { requests: [], total: 0 };
      default:
        query = query.eq('status', 'open');
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      throw error;
    }

    return {
      requests: data || [],
      total: count || 0
    };
  } catch (error) {
    console.error('Get collaboration requests error:', error);
    throw error;
  }
};

// Create collaboration request
const createCollaborationRequest = async (user, requestData) => {
  try {
    if (!user) {
      throw new Error('Authentication required to create requests');
    }

    const { data, error } = await supabase
      .from('collaboration_requests')
      .insert({
        ...requestData,
        created_by: user.id
      })
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Create collaboration request error:', error);
    throw error;
  }
};

// Update collaboration request
const updateCollaborationRequest = async (user, requestId, updateData) => {
  try {
    if (!user) {
      throw new Error('Authentication required to update requests');
    }

    const { data, error } = await supabase
      .from('collaboration_requests')
      .update(updateData)
      .eq('id', requestId)
      .eq('created_by', user.id) // Ensure user owns the request
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Update collaboration request error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user (optional for GET requests)
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    const { type = 'available', limit = 50 } = queryParams;
    
    let result;

    switch (event.httpMethod) {
      case 'GET':
        result = await getCollaborationRequests(type, parseInt(limit), user);
        break;

      case 'POST':
        if (!user) {
          throw new Error('Authentication required');
        }
        const createData = JSON.parse(event.body || '{}');
        result = await createCollaborationRequest(user, createData);
        break;

      case 'PUT':
        if (!user) {
          throw new Error('Authentication required');
        }
        const updateData = JSON.parse(event.body || '{}');
        const requestId = queryParams.id;
        if (!requestId) {
          throw new Error('Request ID is required');
        }
        result = await updateCollaborationRequest(user, requestId, updateData);
        break;

      default:
        throw new Error('Method not allowed');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        ...result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Collaboration Requests API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
