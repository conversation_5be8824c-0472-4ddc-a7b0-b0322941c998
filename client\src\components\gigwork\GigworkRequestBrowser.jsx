import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Input,
  Select,
  SelectItem,
  Button,
  Chip,
  Tabs,
  Tab,
  Avatar,
  Badge,
  Spinner,
  Switch
} from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { checkSkillRequirements } from '../../utils/profile/enhancedProfile.utils';
import { getIntelligentRecommendations } from '../../utils/matching/intelligentMatching.utils';
import { toast } from 'react-hot-toast';
import GigworkApplicationModal from './GigworkApplicationModal';
import IntelligentMatchCard from './IntelligentMatchCard';
import {
  Search,
  Filter,
  DollarSign,
  Clock,
  Users,
  Eye,
  MessageCircle,
  Calendar,
  MapPin,
  Star,
  Award,
  Target,
  CheckCircle,
  Brain,
  Zap
} from 'lucide-react';

/**
 * Gigwork Request Browser Component
 * 
 * Browse and filter available gigwork requests/collaboration opportunities.
 * Integrates with the collaboration_requests API for viewing and applying to requests.
 */
const GigworkRequestBrowser = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [requests, setRequests] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('available');
  const [skillMatches, setSkillMatches] = useState({});

  // Intelligent matching states
  const [intelligentMode, setIntelligentMode] = useState(true);
  const [matchingRequests, setMatchingRequests] = useState([]);
  const [matchingLoading, setMatchingLoading] = useState(false);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [skillFilter, setSkillFilter] = useState('');
  const [projectTypeFilter, setProjectTypeFilter] = useState('all');
  const [experienceFilter, setExperienceFilter] = useState('all');
  const [budgetFilter, setBudgetFilter] = useState('all');
  const [minMatchScore, setMinMatchScore] = useState(30);

  // Application modal state
  const [applicationModalOpen, setApplicationModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);

  // Load requests
  const loadRequests = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/.netlify/functions/collaboration-requests?type=${activeTab}&limit=50`, {
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      const result = await response.json();

      if (response.ok) {
        const requestsData = result.requests || [];
        setRequests(requestsData);

        // Calculate skill matches for each request (legacy mode)
        if (currentUser && !intelligentMode) {
          calculateSkillMatches(requestsData);
        }

        // Calculate intelligent matches if enabled
        if (currentUser && intelligentMode) {
          await calculateIntelligentMatches(requestsData);
        }
      } else {
        throw new Error(result.error || 'Failed to load requests');
      }
    } catch (error) {
      console.error('Error loading requests:', error.message);
      setRequests([]);
      toast.error('Failed to load gigwork requests. Please try again later.');

      // If it's a network error, show a more specific message
      if (error.message.includes('Failed to fetch')) {
        toast.error('Network error: Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Calculate skill matches for all requests (legacy mode)
  const calculateSkillMatches = async (requestsData) => {
    if (!currentUser) return;

    const matches = {};

    for (const request of requestsData) {
      if (request.required_skills && request.required_skills.length > 0) {
        try {
          const skillRequirements = request.required_skills.map(skill => ({
            skill: skill,
            level: 1 // Default minimum level
          }));

          const match = await checkSkillRequirements(currentUser.id, skillRequirements);
          matches[request.id] = match;
        } catch (error) {
          console.error(`Error calculating skill match for request ${request.id}:`, error);
        }
      }
    }

    setSkillMatches(matches);
  };

  // Calculate intelligent matches with AI-powered scoring
  const calculateIntelligentMatches = async (requestsData) => {
    if (!currentUser) return;

    setMatchingLoading(true);
    try {
      const intelligentResults = await getIntelligentRecommendations(
        currentUser.id,
        requestsData,
        {
          minScore: minMatchScore,
          maxResults: 50,
          sortBy: 'score'
        }
      );

      setMatchingRequests(intelligentResults);
    } catch (error) {
      console.error('Error calculating intelligent matches:', error);
      toast.error('Failed to calculate match scores');
      // Fallback to regular requests
      setMatchingRequests(requestsData);
    } finally {
      setMatchingLoading(false);
    }
  };

  // Apply filters
  useEffect(() => {
    const sourceRequests = intelligentMode ? matchingRequests : requests;
    let filtered = sourceRequests;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.project_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.project_description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.required_skills.some(skill =>
          skill.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Skill filter
    if (skillFilter) {
      filtered = filtered.filter(request =>
        request.required_skills.some(skill =>
          skill.toLowerCase().includes(skillFilter.toLowerCase())
        )
      );
    }

    // Project type filter
    if (projectTypeFilter !== 'all') {
      filtered = filtered.filter(request => request.project_type === projectTypeFilter);
    }

    // Experience filter
    if (experienceFilter !== 'all') {
      filtered = filtered.filter(request => request.experience_level === experienceFilter);
    }

    // Budget filter
    if (budgetFilter !== 'all') {
      filtered = filtered.filter(request => {
        const maxBudget = request.budget_range_max || 0;
        switch (budgetFilter) {
          case 'under_1k': return maxBudget < 1000;
          case '1k_5k': return maxBudget >= 1000 && maxBudget <= 5000;
          case '5k_10k': return maxBudget >= 5000 && maxBudget <= 10000;
          case 'over_10k': return maxBudget > 10000;
          default: return true;
        }
      });
    }

    // Match score filter (only in intelligent mode)
    if (intelligentMode && minMatchScore > 0) {
      filtered = filtered.filter(request =>
        (request.matchScore || 0) >= minMatchScore
      );
    }

    setFilteredRequests(filtered);
  }, [requests, matchingRequests, intelligentMode, searchTerm, skillFilter, projectTypeFilter, experienceFilter, budgetFilter, minMatchScore]);

  // Load requests when tab changes or intelligent mode toggles
  useEffect(() => {
    loadRequests();
  }, [activeTab, intelligentMode]);

  // Recalculate matches when minimum score changes
  useEffect(() => {
    if (intelligentMode && currentUser && requests.length > 0) {
      calculateIntelligentMatches(requests);
    }
  }, [minMatchScore]);

  // Handle application to request
  const handleApply = (request) => {
    setSelectedRequest(request);
    setApplicationModalOpen(true);
  };

  // Handle successful application submission
  const handleApplicationSubmitted = (application) => {
    toast.success('Application submitted successfully!');
    // Optionally refresh the requests list or update UI
    loadRequests();
  };

  // Handle view details
  const handleViewDetails = (request) => {
    // TODO: Implement detailed view modal
    console.log('View details for request:', request.id);
  };

  // Handle message
  const handleMessage = (request) => {
    // TODO: Implement messaging functionality
    console.log('Message requester for:', request.id);
  };

  // Format budget display
  const formatBudget = (min, max) => {
    if (!min && !max) return 'Budget not specified';
    if (min === max) return `$${min.toLocaleString()}`;
    return `$${min?.toLocaleString() || '0'} - $${max?.toLocaleString() || 'Open'}`;
  };

  // Get experience level color
  const getExperienceColor = (level) => {
    switch (level) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'secondary';
      case 'expert': return 'danger';
      default: return 'default';
    }
  };

  return (
    <div className={`gigwork-request-browser ${className}`}>
      {/* Header */}
      <Card className="mb-6">
        <CardBody className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2 flex items-center gap-2">
                Browse Gigwork Opportunities
                {intelligentMode && <Brain size={24} className="text-primary" />}
              </h2>
              <p className="text-white/60">
                {intelligentMode
                  ? 'AI-powered matching finds the best opportunities for you'
                  : 'Find projects that match your skills and interests'
                }
              </p>
            </div>

            {/* Quick Stats & Controls */}
            <div className="flex flex-col lg:flex-row items-end lg:items-center gap-4">
              {/* Intelligent Mode Toggle */}
              <div className="flex items-center gap-2">
                <Switch
                  isSelected={intelligentMode}
                  onValueChange={setIntelligentMode}
                  color="primary"
                  startContent={<Brain size={16} />}
                  endContent={<Zap size={16} />}
                >
                  Smart Matching
                </Switch>
              </div>

              {/* Stats */}
              <div className="flex gap-4 text-sm">
                <div className="text-center">
                  <div className="text-xl font-bold text-primary">{filteredRequests.length}</div>
                  <div className="text-white/60">
                    {intelligentMode ? 'Matched' : 'Available'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-success">-</div>
                  <div className="text-white/60">Applied</div>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Filters */}
      <Card className="mb-6">
        <CardBody className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <Input
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startContent={<Search size={16} className="text-default-400" />}
            />

            <Input
              placeholder="Filter by skill..."
              value={skillFilter}
              onChange={(e) => setSkillFilter(e.target.value)}
            />

            <Select
              placeholder="Project Type"
              selectedKeys={projectTypeFilter !== 'all' ? [projectTypeFilter] : []}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0];
                setProjectTypeFilter(selected || 'all');
              }}
            >
              <SelectItem key="all" value="all">All Types</SelectItem>
              <SelectItem key="fixed" value="fixed">Fixed Project</SelectItem>
              <SelectItem key="ongoing" value="ongoing">Ongoing Work</SelectItem>
              <SelectItem key="hourly" value="hourly">Hourly</SelectItem>
            </Select>

            <Select
              placeholder="Experience Level"
              selectedKeys={experienceFilter !== 'all' ? [experienceFilter] : []}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0];
                setExperienceFilter(selected || 'all');
              }}
            >
              <SelectItem key="all" value="all">All Levels</SelectItem>
              <SelectItem key="beginner" value="beginner">Beginner</SelectItem>
              <SelectItem key="intermediate" value="intermediate">Intermediate</SelectItem>
              <SelectItem key="advanced" value="advanced">Advanced</SelectItem>
              <SelectItem key="expert" value="expert">Expert</SelectItem>
            </Select>

            {/* Match Score Filter (only in intelligent mode) */}
            {intelligentMode && (
              <div className="space-y-1">
                <label className="text-xs text-white/60">Min Match Score</label>
                <Select
                  placeholder="Match Score"
                  selectedKeys={[minMatchScore.toString()]}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0];
                    setMinMatchScore(parseInt(selected) || 30);
                  }}
                >
                  <SelectItem key="0" value="0">All Matches</SelectItem>
                  <SelectItem key="30" value="30">30%+ Match</SelectItem>
                  <SelectItem key="50" value="50">50%+ Match</SelectItem>
                  <SelectItem key="70" value="70">70%+ Match</SelectItem>
                  <SelectItem key="85" value="85">85%+ Match</SelectItem>
                </Select>
              </div>
            )}
            
            <Select
              placeholder="Budget Range"
              selectedKeys={budgetFilter !== 'all' ? [budgetFilter] : []}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0];
                setBudgetFilter(selected || 'all');
              }}
            >
              <SelectItem key="all" value="all">All Budgets</SelectItem>
              <SelectItem key="under_1k" value="under_1k">Under $1K</SelectItem>
              <SelectItem key="1k_5k" value="1k_5k">$1K - $5K</SelectItem>
              <SelectItem key="5k_10k" value="5k_10k">$5K - $10K</SelectItem>
              <SelectItem key="over_10k" value="over_10k">Over $10K</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Tabs */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={setActiveTab}
        className="mb-6"
      >
        <Tab key="available" title="Available Opportunities" />
        <Tab key="my_requests" title="My Requests" />
        <Tab key="applications" title="My Applications" />
      </Tabs>

      {/* Request List */}
      {loading || (intelligentMode && matchingLoading) ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Spinner size="lg" />
          <p className="text-white/60 mt-4">
            {intelligentMode && matchingLoading ? 'Calculating intelligent matches...' : 'Loading opportunities...'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AnimatePresence>
            {filteredRequests.map((request, index) =>
              intelligentMode ? (
                <IntelligentMatchCard
                  key={request.id}
                  request={request}
                  onApply={handleApply}
                  onViewDetails={handleViewDetails}
                  onMessage={handleMessage}
                />
              ) : (
                <motion.div
                  key={request.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-white mb-1">
                          {request.project_title}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-white/60">
                          <Avatar
                            src={request.requester?.avatar_url}
                            name={request.requester?.display_name}
                            size="sm"
                          />
                          <span>{request.requester?.display_name}</span>
                          <span>•</span>
                          <span>{new Date(request.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <Chip
                        color={getExperienceColor(request.experience_level)}
                        size="sm"
                        variant="flat"
                      >
                        {request.experience_level}
                      </Chip>
                    </div>
                  </CardHeader>
                  
                  <CardBody className="pt-0">
                    <p className="text-white/80 text-sm mb-4 line-clamp-3">
                      {request.project_description}
                    </p>
                    
                    {/* Skills */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      {request.required_skills.slice(0, 4).map((skill, skillIndex) => (
                        <Chip key={skillIndex} size="sm" variant="flat" color="primary">
                          {skill}
                        </Chip>
                      ))}
                      {request.required_skills.length > 4 && (
                        <Chip size="sm" variant="flat">
                          +{request.required_skills.length - 4} more
                        </Chip>
                      )}
                    </div>

                    {/* Skill Match Indicator */}
                    {skillMatches[request.id] && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs text-white/60">Skill Match</span>
                          <Chip
                            size="sm"
                            color={skillMatches[request.id].qualifies ? 'success' : 'warning'}
                            variant="flat"
                            startContent={
                              skillMatches[request.id].qualifies ?
                                <CheckCircle size={12} /> :
                                <Target size={12} />
                            }
                          >
                            {skillMatches[request.id].matchPercentage}%
                          </Chip>
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1.5">
                          <div
                            className={`h-1.5 rounded-full transition-all duration-300 ${
                              skillMatches[request.id].qualifies ? 'bg-success' : 'bg-warning'
                            }`}
                            style={{ width: `${skillMatches[request.id].matchPercentage}%` }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {/* Project Details */}
                    <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                      <div className="flex items-center gap-2">
                        <DollarSign size={14} className="text-success" />
                        <span>{formatBudget(request.budget_range_min, request.budget_range_max)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock size={14} className="text-warning" />
                        <span>{request.timeline_weeks} weeks</span>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button
                        color="primary"
                        size="sm"
                        onPress={() => handleApply(request)}
                        className="flex-1"
                      >
                        Apply Now
                      </Button>
                      <Button
                        variant="flat"
                        size="sm"
                        isIconOnly
                      >
                        <Eye size={16} />
                      </Button>
                      <Button
                        variant="flat"
                        size="sm"
                        isIconOnly
                      >
                        <MessageCircle size={16} />
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
              )
            )}
          </AnimatePresence>
        </div>
      )}

      {!loading && filteredRequests.length === 0 && (
        <Card>
          <CardBody className="text-center py-12">
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-white mb-2">No opportunities found</h3>
            <p className="text-white/60">
              Try adjusting your filters or check back later for new opportunities.
            </p>
          </CardBody>
        </Card>
      )}

      {/* Application Modal */}
      <GigworkApplicationModal
        isOpen={applicationModalOpen}
        onClose={() => {
          setApplicationModalOpen(false);
          setSelectedRequest(null);
        }}
        request={selectedRequest}
        onApplicationSubmitted={handleApplicationSubmitted}
      />
    </div>
  );
};

export default GigworkRequestBrowser;
