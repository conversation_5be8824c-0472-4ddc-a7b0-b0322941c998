// Teller Bank Account Linking API
// Integration & Services Agent: Core Teller integration for secure bank account linking

const { createClient } = require('@supabase/supabase-js');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Embedded Teller certificates for Netlify deployment
const TELLER_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIExjCCAq6gAwIBAgIIGEd8MoQQDdQwDQYJKoZIhvcNAQELBQAwYTELMAkGA1UE
BhMCR0IxEDAOBgNVBAgMB0VuZ2xhbmQxDzANBgNVBAcMBkxvbmRvbjEPMA0GA1UE
CgwGVGVsbGVyMR4wHAYDVQQLDBVUZWxsZXIgQXBwbGljYXRpb24gQ0EwHhcNMjUw
NjA5MjExMjE2WhcNMjgwNjA4MjExMjE2WjAkMSIwIAYDVQQDDBlhcHBfcGVsazgy
bXJyb2ZwNnVwZGRvMDAwMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
nFDsT/eFcQJo9e8yaGBYl0daxUv4SmGIztzdiAEK7WmjmUqgHnPAg9gxkhWwIEbn
xOev5WYlY7A6q+neAeLJcPotvY5nC0RmTt4RQkhrG0MPLR0Lt50awtE85n/onyfd
gFH/4qI7iiYXs9rHvvY4Bj64EbHPzPw0rDN0tysFU9x/VovqAnPnoxpy2l/RcoZA
+T/4Sz/834/BajwE+mt7X+CzDeAwtm97q9apMXkJDPmaMJwoSUwQCwEvDuj+P+on
d9WX9VzJPh/+WgfraDHDiDvnbz1sT4a2CrN8/P05xJ3DuBfRbWlPPhhyVhNV/JOq
7hF6UJsGUrZYuAYJdsAwKQIDAQABo4G+MIG7MA4GA1UdDwEB/wQEAwIF4DATBgNV
HSUEDDAKBggrBgEFBQcDAjCBkwYDVR0jBIGLMIGIgBSEq++simSLxXkuNSUKjel6
pmhxmqFlpGMwYTELMAkGA1UEBhMCR0IxEDAOBgNVBAgMB0VuZ2xhbmQxDzANBgNV
BAcMBkxvbmRvbjEPMA0GA1UECgwGVGVsbGVyMR4wHAYDVQQLDBVUZWxsZXIgQXBw
bGljYXRpb24gQ0GCCQDiNWG/vm85CTANBgkqhkiG9w0BAQsFAAOCAgEAb6fte3/8
sNnej9xM6mCSfbqmb4zEOEj4PPeOR6OwYO0xTGUxOYFG5wa+APsaotmVYwHq2xnX
47U3QmZKkLAgo6qNM9aX2We3EOw92XmnxOKmf6JhugS7FpxxIO1l9piWuwTZB3SN
Yq3GuSTkQ/0Y0eCpr2FZ75Cx8H8YE6/fw7nnZL1znqV6UOsdkw+x9Q05DvuI9n2i
bU2+O3fwsxW+VZ5G+yD6qsX0B+TZile1CkdDpWLCIAcf73JFyzAbjZpBBR2sUgpP
7ZNjhuLZCyC71+kdHpZb/QmPsfD6tf0grAxnOwCMeMlGEuewdIiuIfy88/3De4Xn
va+6dNefewK81mJf40yPoq23PIyq5w/ABgxLBrMhDzlkH4/l1r3lrWdpLXae7VCl
VntwBtp0o8zMV5qOhe0qsznUjt9qfHId2Mgy0ilMruByvCl3dPUTKdavhdhpr4vJ
tnzWHsW4HdINw7elaaFUjWlBUgRQWCrLMVJNs4yhzxhlgEDz/NqwFCWYX4pfJ/UU
PW+/lz2KpDUb6R2YWwwQKNGR/4pw6e141vmbZt1dOwgIl8+9T0HDt5BRgdD7xyXt
8ry0Bg1FfQAsPrxrSYSiLFrN86eyxardujQjXrn48mxpIx6r065L71qgxTYyly9o
9iB8Ygu9ldRALX5ywwt2TJA0xgJNoaufHo4=
-----END CERTIFICATE-----`;

const TELLER_PRIVATE_KEY = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Teller configuration
const TELLER_CONFIG = {
  environment: process.env.TELLER_ENVIRONMENT || 'sandbox',
  applicationId: process.env.TELLER_APPLICATION_ID,
  baseUrl: process.env.TELLER_ENVIRONMENT === 'production'
    ? 'https://api.teller.io'
    : 'https://api.teller.io',
  certificatePath: process.env.TELLER_CERTIFICATE_PATH || path.join(__dirname, 'teller', 'certificate.pem'),
  privateKeyPath: process.env.TELLER_PRIVATE_KEY_PATH || path.join(__dirname, 'teller', 'private_key.pem')
};

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Create authenticated HTTPS agent for Teller API
const createTellerAgent = () => {
  try {
    // Check if we have Teller credentials configured
    if (!TELLER_CONFIG.applicationId) {
      console.log('Teller Application ID not found. Expected:', 'app_pelk82mrrofp6upddo000');
      console.log('Current TELLER_APPLICATION_ID:', process.env.TELLER_APPLICATION_ID);
      throw new Error('Teller application ID not configured - please set TELLER_APPLICATION_ID environment variable');
    }

    console.log('Teller config check:');
    console.log('- Application ID:', TELLER_CONFIG.applicationId);
    console.log('- Certificate path:', TELLER_CONFIG.certificatePath);
    console.log('- Private key path:', TELLER_CONFIG.privateKeyPath);
    console.log('- Current working directory:', process.cwd());
    console.log('- __dirname:', __dirname);

    // Check if certificate files exist
    const certExists = fs.existsSync(TELLER_CONFIG.certificatePath);
    const keyExists = fs.existsSync(TELLER_CONFIG.privateKeyPath);

    console.log('- Certificate exists:', certExists);
    console.log('- Private key exists:', keyExists);

    let cert, key;

    // Use embedded certificates for Netlify deployment
    console.log('Using embedded Teller certificates');
    cert = Buffer.from(TELLER_CERTIFICATE, 'utf8');
    key = Buffer.from(TELLER_PRIVATE_KEY, 'utf8');

    console.log('Certificate and key loaded successfully');
    console.log('- Certificate length:', cert.length);
    console.log('- Key length:', key.length);

    console.log('Teller certificates loaded successfully');

    return new https.Agent({
      cert,
      key,
      rejectUnauthorized: true
    });
  } catch (error) {
    console.error('Failed to create Teller agent:', error);
    throw new Error('Bank linking service temporarily unavailable - please try again later');
  }
};

// Make authenticated request to Teller API
const tellerRequest = async (endpoint, options = {}) => {
  const agent = createTellerAgent();
  const url = `${TELLER_CONFIG.baseUrl}${endpoint}`;
  
  const requestOptions = {
    method: options.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Teller-Application-Id': TELLER_CONFIG.applicationId,
      ...options.headers
    },
    agent
  };

  if (options.body) {
    requestOptions.body = JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url, requestOptions);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Teller API error: ${data.error || response.statusText}`);
    }
    
    return data;
  } catch (error) {
    console.error('Teller request failed:', error);
    throw error;
  }
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Create link token for bank account connection
// Note: Teller uses a different approach than Plaid - they use direct OAuth flow
const createLinkToken = async (user, products = ['auth', 'transactions', 'transfer'], context = 'personal', projectId = null) => {
  try {
    // Check if Teller is properly configured
    if (!TELLER_CONFIG.applicationId) {
      throw new Error('Bank linking service is being set up. Please check back soon or contact support for assistance.');
    }

    // Check if certificates are available
    try {
      createTellerAgent();
    } catch (certError) {
      throw new Error('Bank linking service is currently being configured. Please try again later or contact support.');
    }

    // Create a proper Teller Connect session using their API
    console.log('Creating Teller Connect session...');
    console.log('Application ID:', TELLER_CONFIG.applicationId);
    console.log('User ID:', user.id);

    try {
      // Use the correct Teller Connect API endpoint
      const connectSessionData = await tellerRequest('/connect/sessions', {
        method: 'POST',
        body: {
          application_id: TELLER_CONFIG.applicationId,
          user_id: user.id,
          redirect_uri: 'https://royalty.technology/#/earn?teller_callback=true',
          webhook_url: process.env.TELLER_WEBHOOK_URL,
          products: products
        }
      });

      console.log('Teller Connect session created:', connectSessionData);

      // Store the session in database for tracking
      const { error: sessionError } = await supabase
        .from('teller_link_sessions')
        .insert({
          user_id: user.id,
          link_token: connectSessionData.session_id || `teller_session_${Date.now()}`,
          products,
          expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          status: 'active'
        });

      if (sessionError) {
        console.error('Failed to store link session:', sessionError);
      }

      // Return the Connect URL from Teller's response
      return {
        authorization_url: connectSessionData.connect_url || connectSessionData.url,
        session_id: connectSessionData.session_id,
        expires_in: 1800
      };

    } catch (tellerError) {
      console.error('Teller API error:', tellerError);

      // If the API call fails, fall back to a direct Connect URL
      // This is based on Teller's documentation for direct integration
      console.log('Falling back to direct Teller Connect URL...');

      // Let's try to test the Teller API directly first to understand the correct approach
      console.log('Testing Teller API endpoints...');

      try {
        // Test if we can reach Teller's API at all
        const testResponse = await tellerRequest('/applications', {
          method: 'GET'
        });
        console.log('Teller API test successful:', testResponse);
      } catch (apiTestError) {
        console.log('Teller API test failed:', apiTestError.message);
      }

      // For now, let's create a simple redirect to Teller's main site with our app ID
      // This should at least confirm if our app ID is valid
      const directConnectUrl = `https://teller.io/connect/${TELLER_CONFIG.applicationId}?redirect_uri=${encodeURIComponent('https://royalty.technology/#/earn?teller_callback=true')}`;

      console.log('Trying simplified Teller Connect URL:', directConnectUrl);

      // Store the fallback session in database for tracking
      const { error: sessionError } = await supabase
        .from('teller_link_sessions')
        .insert({
          user_id: user.id,
          link_token: `teller_direct_${Date.now()}`,
          products,
          expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          status: 'active',
          context: context,
          project_id: projectId
        });

      if (sessionError) {
        console.error('Failed to store link session:', sessionError);
      }

      // Return debug information along with the Connect URL
      return {
        authorization_url: directConnectUrl,
        session_id: `teller_direct_${Date.now()}`,
        expires_in: 1800,
        debug_info: {
          application_id: TELLER_CONFIG.applicationId,
          user_id: user.id,
          redirect_uri: 'https://royalty.technology/#/earn?teller_callback=true',
          attempted_api_call: true,
          fallback_used: true,
          url_format: 'simplified_teller_connect'
        }
      };
    }
  } catch (error) {
    console.error('Create link token error:', error);

    // Provide user-friendly error messages
    if (error.message.includes('certificates') || error.message.includes('configured')) {
      throw error; // Re-throw our custom messages
    } else if (error.message.includes('Teller API')) {
      throw new Error('Bank linking service is temporarily unavailable. Please try again later.');
    } else {
      throw new Error('Unable to initialize bank linking. Please contact support if this issue persists.');
    }
  }
};

// Exchange authorization code for access token (Teller OAuth flow)
const exchangeAuthorizationCode = async (user, authorizationCode, context = 'personal', projectId = null) => {
  try {
    // Exchange authorization code with Teller
    const exchangeData = await tellerRequest('/oauth/token', {
      method: 'POST',
      body: {
        grant_type: 'authorization_code',
        code: authorizationCode,
        client_id: TELLER_CONFIG.applicationId
      }
    });

    // Get account information
    const accountsData = await tellerRequest('/accounts', {
      headers: {
        'Authorization': `Bearer ${exchangeData.access_token}`
      }
    });

    // Store account information in database
    const accountInserts = accountsData.accounts.map(account => ({
      user_id: user.id,
      project_id: context === 'project' ? projectId : null, // Only set project_id for project accounts
      teller_account_id: account.id,
      teller_item_id: exchangeData.item_id,
      teller_access_token: exchangeData.access_token,
      account_type: account.type,
      account_subtype: account.subtype,
      account_name: account.name,
      institution_name: account.institution.name,
      institution_id: account.institution.id,
      account_mask: account.mask,
      available_balance: account.balances?.available || 0,
      current_balance: account.balances?.current || 0,
      supports_ach: account.capabilities?.includes('transfer') || true,
      supports_same_day_ach: account.capabilities?.includes('same_day_ach') || false,
      supports_rtp: account.capabilities?.includes('rtp') || false,
      supports_wire: account.capabilities?.includes('wire') || false,
      is_verified: true,
      is_active: true
    }));

    const { data: accounts, error: accountError } = await supabase
      .from('teller_accounts')
      .insert(accountInserts)
      .select();

    if (accountError) {
      throw new Error(`Failed to store account data: ${accountError.message}`);
    }

    // Update link session status
    await supabase
      .from('teller_link_sessions')
      .update({ 
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('status', 'active');

    return {
      access_token: exchangeData.access_token,
      item_id: exchangeData.item_id,
      accounts: accounts
    };
  } catch (error) {
    console.error('Exchange token error:', error);
    throw error;
  }
};

// Get user's linked accounts
const getLinkedAccounts = async (user) => {
  try {
    const { data: accounts, error } = await supabase
      .from('teller_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`);
    }

    return accounts || [];
  } catch (error) {
    console.error('Get linked accounts error:', error);
    throw error;
  }
};

// Remove linked account
const removeLinkedAccount = async (user, accountId) => {
  try {
    const { error } = await supabase
      .from('teller_accounts')
      .update({ 
        is_active: false,
        removed_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('id', accountId);

    if (error) {
      throw new Error(`Failed to remove account: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Remove account error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};

    let result;

    switch (action) {
      case 'create-link-token':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await createLinkToken(user, body.products, body.context, body.project_id);
        break;

      case 'exchange-token':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        if (!body.authorization_code) {
          throw new Error('Authorization code is required');
        }
        result = await exchangeAuthorizationCode(user, body.authorization_code, body.context, body.project_id);
        break;

      case 'accounts':
        if (httpMethod === 'GET') {
          result = await getLinkedAccounts(user);
        } else if (httpMethod === 'DELETE') {
          const accountId = event.queryStringParameters?.account_id;
          if (!accountId) {
            throw new Error('Account ID is required');
          }
          result = await removeLinkedAccount(user, accountId);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Teller Link API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
